import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/services/notification_manager.dart';

class OffersScreen extends StatefulWidget {
  const OffersScreen({super.key});

  @override
  State<OffersScreen> createState() => _OffersScreenState();
}

class _OffersScreenState extends State<OffersScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _offers = [];
  List<Map<String, dynamic>> _featuredOffers = [];

  @override
  void initState() {
    super.initState();
    _loadOffers();
  }

  Future<void> _loadOffers() async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _featuredOffers = [
        {
          'id': 'featured_001',
          'title': 'New Year Special',
          'subtitle': 'Complete Makeover Package',
          'description': 'Hair styling, facial treatment, manicure & pedicure',
          'originalPrice': 350.0,
          'discountPrice': 199.0,
          'discount': 43,
          'validUntil': '2024-02-29',
          'image': 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400',
          'isFeatured': true,
          'isLimited': true,
        },
        {
          'id': 'featured_002',
          'title': 'Spa Weekend',
          'subtitle': 'Ultimate Relaxation Package',
          'description': 'Full body massage, facial, and spa treatments',
          'originalPrice': 280.0,
          'discountPrice': 179.0,
          'discount': 36,
          'validUntil': '2024-02-15',
          'image': 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400',
          'isFeatured': true,
          'isLimited': false,
        },
      ];

      _offers = [
        {
          'id': 'offer_001',
          'title': 'First Visit Discount',
          'subtitle': '25% Off All Services',
          'description': 'Special discount for new customers on any service',
          'originalPrice': 0.0,
          'discountPrice': 0.0,
          'discount': 25,
          'validUntil': '2024-12-31',
          'image': 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=300',
          'isFeatured': false,
          'isLimited': false,
        },
        {
          'id': 'offer_002',
          'title': 'Hair Color Special',
          'subtitle': 'Professional Hair Coloring',
          'description': 'Premium hair coloring with complimentary styling',
          'originalPrice': 150.0,
          'discountPrice': 99.0,
          'discount': 34,
          'validUntil': '2024-03-15',
          'image': 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=300',
          'isFeatured': false,
          'isLimited': true,
        },
        {
          'id': 'offer_003',
          'title': 'Bridal Package',
          'subtitle': 'Complete Bridal Beauty',
          'description': 'Hair, makeup, nails, and pre-wedding treatments',
          'originalPrice': 450.0,
          'discountPrice': 299.0,
          'discount': 34,
          'validUntil': '2024-06-30',
          'image': 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=300',
          'isFeatured': false,
          'isLimited': false,
        },
        {
          'id': 'offer_004',
          'title': 'Men\'s Grooming',
          'subtitle': 'Complete Men\'s Package',
          'description': 'Haircut, beard trim, facial, and styling',
          'originalPrice': 120.0,
          'discountPrice': 79.0,
          'discount': 34,
          'validUntil': '2024-04-30',
          'image': 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=300',
          'isFeatured': false,
          'isLimited': false,
        },
        {
          'id': 'offer_005',
          'title': 'Student Discount',
          'subtitle': '20% Off All Services',
          'description': 'Special pricing for students with valid ID',
          'originalPrice': 0.0,
          'discountPrice': 0.0,
          'discount': 20,
          'validUntil': '2024-12-31',
          'image': 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=300',
          'isFeatured': false,
          'isLimited': false,
        },
      ];

      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Special Offers',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      floatingActionButton: kDebugMode ? FloatingActionButton(
        onPressed: () async {
          await NotificationManager.sendTestNotification();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test notification sent!'),
              backgroundColor: ThemeConfig.primaryColor,
            ),
          );
        },
        backgroundColor: ThemeConfig.primaryColor,
        child: const Icon(Icons.notifications, color: Colors.white),
      ) : null,
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: ThemeConfig.primaryColor,
              ),
            )
          : RefreshIndicator(
              color: ThemeConfig.primaryColor,
              onRefresh: _loadOffers,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Featured Offers
                    if (_featuredOffers.isNotEmpty) ...[
                      const Text(
                        'Featured Offers',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: ThemeConfig.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 280,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _featuredOffers.length,
                          itemBuilder: (context, index) {
                            final offer = _featuredOffers[index];
                            return Container(
                              width: 300,
                              margin: const EdgeInsets.only(right: 16),
                              child: _buildFeaturedOfferCard(offer),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 32),
                    ],

                    // All Offers
                    const Text(
                      'All Offers',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: ThemeConfig.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _offers.length,
                      itemBuilder: (context, index) {
                        final offer = _offers[index];
                        return _buildOfferCard(offer);
                      },
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildFeaturedOfferCard(Map<String, dynamic> offer) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            ThemeConfig.primaryColor,
            ThemeConfig.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: ThemeConfig.primaryColor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background Image
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Image.network(
              offer['image'] ?? '',
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: ThemeConfig.primaryColor.withValues(alpha: 0.1),
                  child: const Icon(
                    Icons.image,
                    color: Colors.grey,
                    size: 64,
                  ),
                );
              },
            ),
          ),

          // Gradient Overlay
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  Colors.black.withValues(alpha: 0.7),
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.8),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Badges
                Row(
                  children: [
                    if (offer['isLimited'] == true)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'LIMITED',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: ThemeConfig.secondaryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${offer['discount']}% OFF',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const Spacer(),

                // Title and Description
                Text(
                  offer['title'] ?? '',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  offer['subtitle'] ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  offer['description'] ?? '',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),

                // Price and Button
                Row(
                  children: [
                    if (offer['originalPrice'] > 0) ...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '\$${offer['originalPrice'].toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                          Text(
                            '\$${offer['discountPrice'].toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                    const Spacer(),
                    ElevatedButton(
                      onPressed: () {
                        _showOfferDetails(offer);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: ThemeConfig.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 8,
                        ),
                      ),
                      child: const Text(
                        'Book Now',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfferCard(Map<String, dynamic> offer) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image and Discount Badge
          Stack(
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
                child: Image.network(
                  offer['image'] ?? '',
                  width: double.infinity,
                  height: 160,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 160,
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image,
                        color: Colors.grey,
                        size: 48,
                      ),
                    );
                  },
                ),
              ),

              // Discount Badge
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: ThemeConfig.secondaryColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${offer['discount']}% OFF',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              // Limited Badge
              if (offer['isLimited'] == true)
                Positioned(
                  top: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'LIMITED TIME',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  offer['title'] ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: ThemeConfig.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  offer['subtitle'] ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ThemeConfig.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  offer['description'] ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: ThemeConfig.textSecondary,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 12),

                // Price and Valid Until
                Row(
                  children: [
                    if (offer['originalPrice'] > 0) ...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '\$${offer['originalPrice'].toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: ThemeConfig.textSecondary,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                          Text(
                            '\$${offer['discountPrice'].toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: ThemeConfig.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      Text(
                        '${offer['discount']}% Discount',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: ThemeConfig.primaryColor,
                        ),
                      ),
                    ],
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const Text(
                          'Valid until',
                          style: TextStyle(
                            fontSize: 12,
                            color: ThemeConfig.textSecondary,
                          ),
                        ),
                        Text(
                          offer['validUntil'] ?? '',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: ThemeConfig.textPrimary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      _showOfferDetails(offer);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeConfig.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Book This Offer',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showOfferDetails(Map<String, dynamic> offer) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Image.network(
                        offer['image'] ?? '',
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 200,
                            color: Colors.grey[300],
                            child: const Icon(
                              Icons.image,
                              color: Colors.grey,
                              size: 64,
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Title and Badges
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            offer['title'] ?? '',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: ThemeConfig.textPrimary,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: ThemeConfig.secondaryColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${offer['discount']}% OFF',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    Text(
                      offer['subtitle'] ?? '',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: ThemeConfig.primaryColor,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Description
                    Text(
                      offer['description'] ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        color: ThemeConfig.textSecondary,
                        height: 1.5,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Price Information
                    if (offer['originalPrice'] > 0) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Original Price',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: ThemeConfig.textSecondary,
                                  ),
                                ),
                                Text(
                                  '\$${offer['originalPrice'].toStringAsFixed(0)}',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    color: ThemeConfig.textSecondary,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(width: 32),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Offer Price',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: ThemeConfig.textSecondary,
                                  ),
                                ),
                                Text(
                                  '\$${offer['discountPrice'].toStringAsFixed(0)}',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: ThemeConfig.primaryColor,
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                const Text(
                                  'You Save',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: ThemeConfig.textSecondary,
                                  ),
                                ),
                                Text(
                                  '\$${(offer['originalPrice'] - offer['discountPrice']).toStringAsFixed(0)}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Valid Until
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.orange.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.schedule,
                            color: Colors.orange,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Valid until ${offer['validUntil']}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),

            // Bottom Action
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // TODO: Navigate to booking screen with offer
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Booking with offer coming soon!'),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ThemeConfig.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Book This Offer Now',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
