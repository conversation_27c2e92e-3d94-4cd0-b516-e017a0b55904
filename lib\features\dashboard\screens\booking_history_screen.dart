import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/routes/app_routes.dart';

class BookingHistoryScreen extends StatefulWidget {
  const BookingHistoryScreen({super.key});

  @override
  State<BookingHistoryScreen> createState() => _BookingHistoryScreenState();
}

class _BookingHistoryScreenState extends State<BookingHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _allBookings = [];
  List<Map<String, dynamic>> _upcomingBookings = [];
  List<Map<String, dynamic>> _pastBookings = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadBookings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _allBookings = [
        {
          'id': 'booking_001',
          'serviceName': 'Premium Facial Treatment',
          'date': '2024-01-22',
          'time': '2:00 PM',
          'status': 'upcoming',
          'amount': 150.0,
          'staffName': 'Emily Davis',
          'staffImage': 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=200',
          'duration': 90,
          'serviceImage': 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=300',
        },
        {
          'id': 'booking_002',
          'serviceName': 'Hair Cut & Styling',
          'date': '2024-01-15',
          'time': '10:30 AM',
          'status': 'completed',
          'amount': 80.0,
          'staffName': 'Sarah Wilson',
          'staffImage': 'https://images.unsplash.com/photo-1594824388853-d0c2d5e5b6b8?w=200',
          'duration': 60,
          'serviceImage': 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=300',
        },
        {
          'id': 'booking_003',
          'serviceName': 'Manicure & Pedicure',
          'date': '2024-01-08',
          'time': '3:30 PM',
          'status': 'completed',
          'amount': 120.0,
          'staffName': 'Jessica Brown',
          'staffImage': 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=200',
          'duration': 75,
          'serviceImage': 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=300',
        },
        {
          'id': 'booking_004',
          'serviceName': 'Deep Tissue Massage',
          'date': '2024-01-01',
          'time': '11:00 AM',
          'status': 'cancelled',
          'amount': 200.0,
          'staffName': 'Michael Johnson',
          'staffImage': 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=200',
          'duration': 120,
          'serviceImage': 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=300',
        },
        {
          'id': 'booking_005',
          'serviceName': 'Eyebrow Threading',
          'date': '2023-12-20',
          'time': '4:00 PM',
          'status': 'completed',
          'amount': 45.0,
          'staffName': 'Lisa Chen',
          'staffImage': 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=200',
          'duration': 30,
          'serviceImage': 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=300',
        },
      ];

      _upcomingBookings = _allBookings
          .where((booking) => booking['status'] == 'upcoming')
          .toList();

      _pastBookings = _allBookings
          .where((booking) => 
              booking['status'] == 'completed' || booking['status'] == 'cancelled')
          .toList();

      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'My Bookings',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: ThemeConfig.primaryColor,
          unselectedLabelColor: ThemeConfig.textSecondary,
          indicatorColor: ThemeConfig.primaryColor,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Upcoming'),
            Tab(text: 'Past'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: ThemeConfig.primaryColor,
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBookingsList(_allBookings),
                _buildBookingsList(_upcomingBookings),
                _buildBookingsList(_pastBookings),
              ],
            ),
    );
  }

  Widget _buildBookingsList(List<Map<String, dynamic>> bookings) {
    if (bookings.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      color: ThemeConfig.primaryColor,
      onRefresh: _loadBookings,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bookings.length,
        itemBuilder: (context, index) {
          final booking = bookings[index];
          return _buildBookingCard(booking);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No bookings found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Book your first appointment to get started',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              AppRoutes.pushNamed(context, AppRoutes.services);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeConfig.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text('Book Now'),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingCard(Map<String, dynamic> booking) {
    final status = booking['status'] ?? '';
    final statusColor = _getStatusColor(status);
    final statusText = _getStatusText(status);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            AppRoutes.pushNamed(
              context,
              AppRoutes.bookingDetails,
              arguments: {'bookingId': booking['id']},
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Booking #${booking['id']?.substring(booking['id'].length - 6) ?? ''}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: ThemeConfig.textSecondary,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        statusText,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: statusColor,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Service info
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.network(
                        booking['serviceImage'] ?? '',
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.spa,
                              color: Colors.grey,
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            booking['serviceName'] ?? '',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: ThemeConfig.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'with ${booking['staffName'] ?? 'Staff'}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: ThemeConfig.textSecondary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${booking['duration'] ?? 0} minutes',
                            style: const TextStyle(
                              fontSize: 12,
                              color: ThemeConfig.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '\$${booking['amount']?.toStringAsFixed(0) ?? '0'}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: ThemeConfig.primaryColor,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Date and time
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${booking['date']} at ${booking['time']}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey[400],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'upcoming':
      case 'confirmed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'Completed';
      case 'upcoming':
        return 'Upcoming';
      case 'confirmed':
        return 'Confirmed';
      case 'cancelled':
        return 'Cancelled';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  }
}
