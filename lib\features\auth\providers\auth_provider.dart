import 'package:flutter/foundation.dart';

import '../../../core/models/user.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/api_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  
  User? _currentUser;
  bool _isLoading = false;
  String? _error;
  bool _isAuthenticated = false;
  
  AuthProvider({required AuthService authService}) : _authService = authService {
    _initializeAuth();
  }
  
  // Getters
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _isAuthenticated;
  
  // Initialize authentication state
  Future<void> _initializeAuth() async {
    _setLoading(true);
    
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          _currentUser = user;
          _isAuthenticated = true;
        }
      }
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // Login
  Future<bool> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.login(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );
      
      if (response.isSuccess && response.data != null) {
        _currentUser = response.data;
        _isAuthenticated = true;
        notifyListeners();
        return true;
      } else {
        _setError(response.error ?? 'Login failed');
        return false;
      }
    } catch (e) {
      _setError('Login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Register
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String confirmPassword,
    String? phone,
    String? referralCode,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.register(
        name: name,
        email: email,
        password: password,
        confirmPassword: confirmPassword,
        phone: phone,
        referralCode: referralCode,
      );
      
      if (response.isSuccess) {
        // Registration successful, but user might need to verify email
        return true;
      } else {
        _setError(response.error ?? 'Registration failed');
        return false;
      }
    } catch (e) {
      _setError('Registration failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Forgot Password
  Future<bool> forgotPassword({required String email}) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.forgotPassword(email: email);
      
      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.error ?? 'Failed to send reset email');
        return false;
      }
    } catch (e) {
      _setError('Failed to send reset email: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Reset Password
  Future<bool> resetPassword({
    required String token,
    required String password,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.resetPassword(
        token: token,
        password: password,
        confirmPassword: confirmPassword,
      );
      
      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.error ?? 'Password reset failed');
        return false;
      }
    } catch (e) {
      _setError('Password reset failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Verify OTP
  Future<bool> verifyOtp({
    required String email,
    required String otp,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.verifyOtp(
        email: email,
        otp: otp,
      );
      
      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.error ?? 'OTP verification failed');
        return false;
      }
    } catch (e) {
      _setError('OTP verification failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Logout
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _authService.logout();
      _currentUser = null;
      _isAuthenticated = false;
      notifyListeners();
    } catch (e) {
      // Even if logout API fails, clear local state
      _currentUser = null;
      _isAuthenticated = false;
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }
  
  // Refresh user data
  Future<void> refreshUserData() async {
    if (!_isAuthenticated) return;
    
    try {
      final response = await _authService.refreshUserData();
      if (response.isSuccess && response.data != null) {
        _currentUser = response.data;
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to refresh user data: $e');
      }
    }
  }
  
  // Update profile
  Future<bool> updateProfile({
    String? name,
    String? phone,
    DateTime? dateOfBirth,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.updateProfile(
        name: name,
        phone: phone,
        dateOfBirth: dateOfBirth,
      );
      
      if (response.isSuccess && response.data != null) {
        _currentUser = response.data;
        notifyListeners();
        return true;
      } else {
        _setError(response.error ?? 'Profile update failed');
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      final response = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );
      
      if (response.isSuccess) {
        return true;
      } else {
        _setError(response.error ?? 'Password change failed');
        return false;
      }
    } catch (e) {
      _setError('Password change failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }
  
  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
    notifyListeners();
  }
  
  // Clear error manually
  void clearError() {
    _clearError();
  }
}
