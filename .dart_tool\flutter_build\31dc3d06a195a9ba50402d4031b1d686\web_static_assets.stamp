{"inputs": ["E:\\flutter\\flutter_windows_3.32.0-stable\\flutter\\bin\\cache\\engine.stamp"], "outputs": ["E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\flutter.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\canvaskit.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\canvaskit.js.symbols", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\canvaskit.wasm", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\chromium\\canvaskit.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\chromium\\canvaskit.js.symbols", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\chromium\\canvaskit.wasm", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\skwasm.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\skwasm.js.symbols", "E:\\FlixApp\\.dart_tool\\flutter_build\\31dc3d06a195a9ba50402d4031b1d686\\canvaskit\\skwasm.wasm"]}