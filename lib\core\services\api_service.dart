// import 'dart:convert'; // Unused
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../config/app_config.dart';
import '../utils/app_constants.dart';
import 'storage_service.dart';

class ApiService {
  late final Dio _dio;
  
  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.baseUrl,
      connectTimeout: AppConfig.connectionTimeout,
      receiveTimeout: AppConfig.receiveTimeout,
      sendTimeout: AppConfig.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    // Request interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        final token = await StorageService.getString(AppConstants.userTokenKey);
        if (token != null && token.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        
        if (kDebugMode) {
          print('🚀 REQUEST: ${options.method} ${options.uri}');
          print('📤 Headers: ${options.headers}');
          if (options.data != null) {
            print('📤 Data: ${options.data}');
          }
        }
        
        handler.next(options);
      },
      
      onResponse: (response, handler) {
        if (kDebugMode) {
          print('✅ RESPONSE: ${response.statusCode} ${response.requestOptions.uri}');
          print('📥 Data: ${response.data}');
        }
        handler.next(response);
      },
      
      onError: (error, handler) {
        if (kDebugMode) {
          print('❌ ERROR: ${error.requestOptions.uri}');
          print('❌ Message: ${error.message}');
          print('❌ Response: ${error.response?.data}');
        }
        handler.next(error);
      },
    ));
  }
  
  // Generic GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error('An unexpected error occurred: $e');
    }
  }
  
  // Generic POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error('An unexpected error occurred: $e');
    }
  }
  
  // Generic PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error('An unexpected error occurred: $e');
    }
  }
  
  // Generic DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final response = await _dio.delete(
        endpoint,
        queryParameters: queryParameters,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error('An unexpected error occurred: $e');
    }
  }
  
  // File upload
  Future<ApiResponse<T>> uploadFile<T>(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final formData = FormData();
      
      // Add file
      formData.files.add(MapEntry(
        fieldName,
        await MultipartFile.fromFile(file.path),
      ));
      
      // Add additional data
      if (additionalData != null) {
        additionalData.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }
      
      final response = await _dio.post(
        endpoint,
        data: formData,
      );
      
      return _handleResponse<T>(response, fromJson);
    } on DioException catch (e) {
      return _handleError<T>(e);
    } catch (e) {
      return ApiResponse<T>.error('An unexpected error occurred: $e');
    }
  }
  
  // Handle successful response
  ApiResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic)? fromJson,
  ) {
    final data = response.data;
    
    // Check if response indicates success
    if (data is Map<String, dynamic>) {
      final success = data[AppConstants.successKey] ?? true;
      final message = data[AppConstants.messageKey] ?? '';
      final responseData = data[AppConstants.dataKey];
      
      if (success == true || success == 1) {
        if (fromJson != null && responseData != null) {
          try {
            final parsedData = fromJson(responseData);
            return ApiResponse<T>.success(parsedData, message);
          } catch (e) {
            return ApiResponse<T>.error('Failed to parse response data: $e');
          }
        } else {
          return ApiResponse<T>.success(responseData as T?, message);
        }
      } else {
        return ApiResponse<T>.error(message.isNotEmpty ? message : 'Request failed');
      }
    }
    
    // If response is not in expected format, return raw data
    if (fromJson != null) {
      try {
        final parsedData = fromJson(data);
        return ApiResponse<T>.success(parsedData);
      } catch (e) {
        return ApiResponse<T>.error('Failed to parse response data: $e');
      }
    }
    
    return ApiResponse<T>.success(data as T?);
  }
  
  // Handle error response
  ApiResponse<T> _handleError<T>(DioException error) {
    String message;
    
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        message = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final responseData = error.response?.data;
        
        if (responseData is Map<String, dynamic>) {
          message = responseData[AppConstants.messageKey] ?? 
                   responseData[AppConstants.errorKey] ?? 
                   'Server error occurred';
        } else {
          message = 'Server error: $statusCode';
        }
        break;
      case DioExceptionType.cancel:
        message = 'Request was cancelled';
        break;
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          message = 'No internet connection';
        } else {
          message = 'An unexpected error occurred';
        }
        break;
      default:
        message = 'An unexpected error occurred';
    }
    
    return ApiResponse<T>.error(message);
  }
}

// API Response wrapper class
class ApiResponse<T> {
  final bool isSuccess;
  final T? data;
  final String message;
  final String? error;
  
  ApiResponse._({
    required this.isSuccess,
    this.data,
    this.message = '',
    this.error,
  });
  
  factory ApiResponse.success(T? data, [String message = '']) {
    return ApiResponse._(
      isSuccess: true,
      data: data,
      message: message,
    );
  }
  
  factory ApiResponse.error(String error) {
    return ApiResponse._(
      isSuccess: false,
      error: error,
    );
  }
  
  bool get isError => !isSuccess;
}
