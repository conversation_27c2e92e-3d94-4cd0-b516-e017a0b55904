import 'package:hive/hive.dart';

part 'user.g.dart';

@HiveType(typeId: 0)
class User extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String? name;

  @HiveField(2)
  final String email;

  @HiveField(3)
  final String? image;

  @HiveField(4)
  final String? phone;

  @HiveField(5)
  final DateTime? dateOfBirth;

  @HiveField(6)
  final UserRole role;

  @HiveField(7)
  final int points;

  @HiveField(8)
  final String? referralCode;

  @HiveField(9)
  final String? referredBy;

  @HiveField(10)
  final DateTime createdAt;

  @HiveField(11)
  final DateTime updatedAt;

  @HiveField(12)
  final bool isActive;

  @HiveField(13)
  final DateTime? emailVerifiedAt;

  User({
    required this.id,
    this.name,
    required this.email,
    this.image,
    this.phone,
    this.dateOfBirth,
    required this.role,
    this.points = 0,
    this.referralCode,
    this.referredBy,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.emailVerifiedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      name: json['name'],
      email: json['email'] ?? '',
      image: json['image'],
      phone: json['phone'],
      dateOfBirth: json['date_of_birth'] != null 
          ? DateTime.parse(json['date_of_birth']) 
          : null,
      role: UserRole.fromString(json['role'] ?? 'CUSTOMER'),
      points: json['points'] ?? 0,
      referralCode: json['referral_code'],
      referredBy: json['referred_by'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      emailVerifiedAt: json['email_verified_at'] != null 
          ? DateTime.parse(json['email_verified_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'image': image,
      'phone': phone,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'role': role.value,
      'points': points,
      'referral_code': referralCode,
      'referred_by': referredBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
      'email_verified_at': emailVerifiedAt?.toIso8601String(),
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? image,
    String? phone,
    DateTime? dateOfBirth,
    UserRole? role,
    int? points,
    String? referralCode,
    String? referredBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    DateTime? emailVerifiedAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      role: role ?? this.role,
      points: points ?? this.points,
      referralCode: referralCode ?? this.referralCode,
      referredBy: referredBy ?? this.referredBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
    );
  }

  bool get isCustomer => role == UserRole.customer;
  bool get isStaff => role == UserRole.staff;
  bool get isAdmin => role == UserRole.admin;
  bool get isEmailVerified => emailVerifiedAt != null;
  
  String get displayName => name ?? email.split('@').first;
  String? get profileImage => image; // Alias for backward compatibility
  String get initials {
    if (name != null && name!.isNotEmpty) {
      final parts = name!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name![0].toUpperCase();
    }
    return email[0].toUpperCase();
  }
}

@HiveType(typeId: 1)
enum UserRole {
  @HiveField(0)
  customer('CUSTOMER'),
  @HiveField(1)
  admin('ADMIN'),
  @HiveField(2)
  staff('STAFF');

  const UserRole(this.value);
  final String value;

  static UserRole fromString(String value) {
    switch (value.toUpperCase()) {
      case 'CUSTOMER':
        return UserRole.customer;
      case 'ADMIN':
        return UserRole.admin;
      case 'STAFF':
        return UserRole.staff;
      default:
        return UserRole.customer;
    }
  }

  @override
  String toString() => value;
}
