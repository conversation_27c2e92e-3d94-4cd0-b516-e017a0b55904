import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'package:timezone/timezone.dart' as tz;

// Conditional import for Firebase messaging (not supported on web)
import 'package:firebase_messaging/firebase_messaging.dart'
    if (dart.library.html) 'notification_service_web.dart' as firebase;

import '../utils/app_constants.dart';
import 'storage_service.dart';

class NotificationService {
  static firebase.FirebaseMessaging? _firebaseMessaging;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  
  // Initialize notification service
  static Future<void> init() async {
    // Initialize Firebase messaging only on mobile platforms
    if (!kIsWeb) {
      _firebaseMessaging = firebase.FirebaseMessaging.instance;

      // Request permission for notifications
      await _requestPermission();

      // Configure Firebase messaging
      await _configureFirebaseMessaging();

      // Get FCM token
      await _getFCMToken();
    }

    // Initialize local notifications (works on all platforms)
    await _initializeLocalNotifications();
  }
  
  // Request notification permission
  static Future<void> _requestPermission() async {
    if (_firebaseMessaging == null) return;

    final settings = await _firebaseMessaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
    
    if (kDebugMode) {
      print('Notification permission status: ${settings.authorizationStatus}');
    }
    
    // Save permission status
    await StorageService.setBool(
      AppConstants.notificationsEnabledKey,
      settings.authorizationStatus == AuthorizationStatus.authorized,
    );
  }
  
  // Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }
  
  // Configure Firebase messaging
  static Future<void> _configureFirebaseMessaging() async {
    if (_firebaseMessaging == null || kIsWeb) return;

    // Handle foreground messages
    firebase.FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages (not supported on web)
    if (!kIsWeb) {
      firebase.FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    }

    // Handle notification taps when app is in background
    firebase.FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    final initialMessage = await _firebaseMessaging!.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }
  
  // Get FCM token
  static Future<String?> _getFCMToken() async {
    if (_firebaseMessaging == null) return null;

    try {
      final token = await _firebaseMessaging!.getToken();
      if (kDebugMode) {
        print('FCM Token: $token');
      }
      return token;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }
  
  // Handle foreground messages
  static Future<void> _handleForegroundMessage(firebase.RemoteMessage message) async {
    if (kDebugMode) {
      print('Received foreground message: ${message.messageId}');
      print('Title: ${message.notification?.title}');
      print('Body: ${message.notification?.body}');
      print('Data: ${message.data}');
    }
    
    // Show local notification for foreground messages
    await _showLocalNotification(message);
  }
  
  // Handle background messages
  static Future<void> _handleBackgroundMessage(firebase.RemoteMessage message) async {
    if (kDebugMode) {
      print('Received background message: ${message.messageId}');
    }
  }
  
  // Handle notification tap
  static void _handleNotificationTap(firebase.RemoteMessage message) {
    if (kDebugMode) {
      print('Notification tapped: ${message.messageId}');
      print('Data: ${message.data}');
    }
    
    // Navigate to appropriate screen based on notification data
    _navigateFromNotification(message.data);
  }
  
  // Handle local notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('Local notification tapped: ${response.id}');
      print('Payload: ${response.payload}');
    }

    // Handle local notification tap
    if (response.payload != null) {
      final parts = response.payload!.split('|');
      if (parts.length >= 3) {
        final type = parts[0];
        final id = parts[1];
        final action = parts[2];

        _storeNavigationData(type, id, action);
      }
    }
  }
  
  // Show local notification
  static Future<void> _showLocalNotification(firebase.RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'flix_channel',
      'Flix Notifications',
      channelDescription: 'Notifications from Flix Salon & SPA',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Flix Salon & SPA',
      message.notification?.body ?? '',
      notificationDetails,
      payload: message.data.toString(),
    );
  }
  
  // Navigate from notification
  static void _navigateFromNotification(Map<String, dynamic> data) {
    final type = data['type'];
    final notificationId = data['id'];
    final action = data['action'];

    if (kDebugMode) {
      print('Navigating from notification - Type: $type, ID: $notificationId, Action: $action');
    }

    // Store navigation data for later use when app becomes active
    _storeNavigationData(type, notificationId, action);
  }

  // Store navigation data for handling when app becomes active
  static Future<void> _storeNavigationData(String? type, String? id, String? action) async {
    if (type != null) {
      await StorageService.setString('pending_notification_type', type);
    }
    if (id != null) {
      await StorageService.setString('pending_notification_id', id);
    }
    if (action != null) {
      await StorageService.setString('pending_notification_action', action);
    }
  }

  // Get and clear pending navigation data
  static Future<Map<String, String?>> getPendingNavigationData() async {
    final type = await StorageService.getString('pending_notification_type');
    final id = await StorageService.getString('pending_notification_id');
    final action = await StorageService.getString('pending_notification_action');

    // Clear the stored data
    await StorageService.remove('pending_notification_type');
    await StorageService.remove('pending_notification_id');
    await StorageService.remove('pending_notification_action');

    return {
      'type': type,
      'id': id,
      'action': action,
    };
  }
  
  // Schedule local notification
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'flix_channel',
      'Flix Notifications',
      channelDescription: 'Notifications from Flix Salon & SPA',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails();

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Convert DateTime to TZDateTime
    final tzScheduledDate = tz.TZDateTime.from(scheduledDate, tz.local);

    await _localNotifications.zonedSchedule(
      id,
      title,
      body,
      tzScheduledDate,
      notificationDetails,
      payload: payload,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }
  
  // Cancel notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }
  
  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }
  
  // Get FCM token for current device
  static Future<String?> getFCMToken() async {
    return await _getFCMToken();
  }
  
  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    if (_firebaseMessaging != null) {
      await _firebaseMessaging!.subscribeToTopic(topic);
    }
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    if (_firebaseMessaging != null) {
      await _firebaseMessaging!.unsubscribeFromTopic(topic);
    }
  }
  
  // Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    final enabled = await StorageService.getBool(AppConstants.notificationsEnabledKey);
    return enabled ?? false;
  }
  
  // Enable/disable notifications
  static Future<void> setNotificationsEnabled(bool enabled) async {
    await StorageService.setBool(AppConstants.notificationsEnabledKey, enabled);
    
    if (enabled) {
      await _requestPermission();
    }
  }
}
