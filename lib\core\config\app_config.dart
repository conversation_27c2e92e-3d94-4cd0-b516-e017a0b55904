class AppConfig {
  // API Configuration
  static const String baseUrl = 'http://localhost/flix'; // Local development
  // static const String baseUrl = 'https://your-live-server.com/flix'; // Production
  
  static const String apiVersion = 'v1';
  static const String apiPath = '/api';
  
  // Full API URL
  static String get apiUrl => '$baseUrl$apiPath';
  
  // Endpoints
  static const String authEndpoint = '/auth';
  static const String servicesEndpoint = '/services.php';
  static const String customerEndpoint = '/customer';
  static const String bookingEndpoint = '/booking';
  static const String reviewsEndpoint = '/reviews.php';
  static const String newsletterEndpoint = '/newsletter.php';
  static const String wishlistEndpoint = '/wishlist.php';
  
  // Auth endpoints
  static String get loginUrl => '$baseUrl$authEndpoint/login.php';
  static String get registerUrl => '$baseUrl$authEndpoint/register.php';
  static String get forgotPasswordUrl => '$baseUrl$authEndpoint/forgot-password.php';
  static String get resetPasswordUrl => '$baseUrl$authEndpoint/reset-password.php';
  static String get verifyOtpUrl => '$baseUrl$authEndpoint/verify-otp.php';
  static String get logoutUrl => '$baseUrl$authEndpoint/logout.php';
  
  // Customer API endpoints
  static String get searchServicesUrl => '$apiUrl$customerEndpoint/search-services.php';
  static String get serviceVariationsUrl => '$apiUrl$customerEndpoint/service_variations.php';
  static String get suggestStaffUrl => '$apiUrl$customerEndpoint/suggest-staff.php';
  static String get timeSlotsUrl => '$apiUrl$customerEndpoint/time-slots.php';
  static String get validateAvailabilityUrl => '$apiUrl$customerEndpoint/validate-availability.php';
  static String get subcategoriesUrl => '$apiUrl$customerEndpoint/get-subcategories.php';
  
  // Other endpoints
  static String get servicesUrl => '$apiUrl$servicesEndpoint';
  static String get reviewsUrl => '$apiUrl$reviewsEndpoint';
  static String get newsletterUrl => '$apiUrl$newsletterEndpoint';
  static String get wishlistUrl => '$apiUrl$wishlistEndpoint';
  
  // App Configuration
  static const String appName = 'Flix Salon & SPA';
  static const String appVersion = '1.0.0';
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '(255) 745 456-789';
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100; // MB
  
  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 50;
  
  // Image Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Booking Configuration
  static const int maxAdvanceBookingDays = 30;
  static const int minCancellationHours = 24;
  
  // Points Configuration
  static const int pointsEarnRate = 1; // Points per TSH spent
  static const int pointsRedeemRate = 100; // Points per TSH value
  
  // Environment
  static const bool isProduction = false;
  static const bool enableLogging = true;
  static const bool enableCrashReporting = false;
  
  // Feature Flags
  static const bool enablePushNotifications = true;
  static const bool enableBiometricAuth = true;
  static const bool enableSocialLogin = false;
  static const bool enableOfflineMode = true;
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
}
