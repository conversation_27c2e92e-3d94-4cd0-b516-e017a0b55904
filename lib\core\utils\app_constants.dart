class AppConstants {
  // App Information
  static const String appName = 'Flix Salon & SPA';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Luxury Beauty Salon & SPA Services';
  
  // Business Information
  static const String businessName = 'Flix Salon & SPA';
  static const String businessAddress = 'Upanga, Dar Es Salaam, Tanzania';
  static const String businessPhone = '(255) 745 456-789';
  static const String businessEmail = '<EMAIL>';
  static const String businessHours = 'Mon-Sat: 9AM-7PM, Sun: 10AM-6PM';
  
  // Social Media
  static const String instagramUrl = 'https://www.instagram.com/flix.tz/';
  static const String facebookUrl = 'https://www.facebook.com/flixsalonspa';
  static const String twitterUrl = 'https://www.twitter.com/flixsalonspa';
  static const String websiteUrl = 'https://flixsalonspa.com';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String isLoggedInKey = 'is_logged_in';
  static const String userRoleKey = 'user_role';
  static const String userIdKey = 'user_id';
  static const String rememberMeKey = 'remember_me';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String onboardingCompletedKey = 'onboarding_completed';
  
  // Cache Keys
  static const String servicesCacheKey = 'services_cache';
  static const String packagesCacheKey = 'packages_cache';
  static const String galleryCacheKey = 'gallery_cache';
  static const String offersCacheKey = 'offers_cache';
  static const String reviewsCacheKey = 'reviews_cache';
  static const String blogCacheKey = 'blog_cache';
  
  // API Response Keys
  static const String successKey = 'success';
  static const String messageKey = 'message';
  static const String dataKey = 'data';
  static const String errorKey = 'error';
  static const String errorsKey = 'errors';
  static const String tokenKey = 'token';
  static const String userKey = 'user';
  
  // User Roles
  static const String customerRole = 'CUSTOMER';
  static const String adminRole = 'ADMIN';
  static const String staffRole = 'STAFF';
  
  // Booking Status
  static const String pendingStatus = 'PENDING';
  static const String confirmedStatus = 'CONFIRMED';
  static const String inProgressStatus = 'IN_PROGRESS';
  static const String completedStatus = 'COMPLETED';
  static const String cancelledStatus = 'CANCELLED';
  static const String noShowStatus = 'NO_SHOW';
  static const String expiredStatus = 'EXPIRED';
  
  // Service Categories
  static const List<String> serviceCategories = [
    'Hair',
    'Facial',
    'Nails',
    'Massage',
    'Beauty',
    'Makeup',
    'Eyebrows',
    'Lashes',
    'Hair Removal',
    'Body Treatments',
  ];
  
  // Payment Methods
  static const String cardPayment = 'card';
  static const String mobilePayment = 'mobile';
  static const String cashPayment = 'cash';
  
  // Notification Types
  static const String bookingNotification = 'booking';
  static const String promotionNotification = 'promotion';
  static const String reminderNotification = 'reminder';
  static const String generalNotification = 'general';
  
  // Image Placeholders
  static const String defaultProfileImage = 'assets/images/default_profile.png';
  static const String defaultServiceImage = 'assets/images/default_service.png';
  static const String defaultGalleryImage = 'assets/images/default_gallery.png';
  static const String logoImage = 'assets/images/logo.png';
  static const String splashImage = 'assets/images/splash.png';
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Validation Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^[+]?[0-9]{10,15}$';
  static const String passwordPattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  
  // Error Messages
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'An unknown error occurred. Please try again.';
  static const String invalidCredentials = 'Invalid email or password.';
  static const String emailRequired = 'Email is required.';
  static const String passwordRequired = 'Password is required.';
  static const String nameRequired = 'Name is required.';
  static const String phoneRequired = 'Phone number is required.';
  static const String invalidEmail = 'Please enter a valid email address.';
  static const String invalidPhone = 'Please enter a valid phone number.';
  static const String weakPassword = 'Password must be at least 8 characters with uppercase, lowercase, and number.';
  static const String passwordMismatch = 'Passwords do not match.';
  
  // Success Messages
  static const String loginSuccess = 'Login successful!';
  static const String registerSuccess = 'Registration successful!';
  static const String bookingSuccess = 'Booking confirmed successfully!';
  static const String profileUpdateSuccess = 'Profile updated successfully!';
  static const String passwordResetSuccess = 'Password reset link sent to your email.';
  static const String otpSentSuccess = 'OTP sent to your phone number.';
  
  // Currencies
  static const String currency = 'TSH';
  static const String currencySymbol = 'TSH ';
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String displayDateFormat = 'EEEE, MMMM dd, yyyy';
  static const String displayTimeFormat = 'h:mm a';
  
  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 50;
  
  // Rating
  static const double maxRating = 5.0;
  static const double minRating = 1.0;
  
  // Loyalty Points
  static const int pointsPerTsh = 1;
  static const int tshPerPoint = 100;
  
  // Booking Limits
  static const int maxAdvanceBookingDays = 30;
  static const int minCancellationHours = 24;
  
  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Feature Flags
  static const bool enableBiometric = true;
  static const bool enablePushNotifications = true;
  static const bool enableSocialLogin = false;
  static const bool enableOfflineMode = true;
  static const bool enableDarkMode = false;
}
