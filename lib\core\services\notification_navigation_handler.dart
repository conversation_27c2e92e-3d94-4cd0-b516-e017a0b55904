import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

import '../utils/app_constants.dart';
import '../routes/app_routes.dart';
import 'notification_service.dart';

class NotificationNavigationHandler {
  static GlobalKey<NavigatorState>? _navigator<PERSON>ey;
  
  /// Initialize with navigator key
  static void initialize(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
  }
  
  /// Handle pending notification navigation
  static Future<void> handlePendingNavigation() async {
    if (_navigatorKey?.currentContext == null) return;
    
    final navigationData = await NotificationService.getPendingNavigationData();
    final type = navigationData['type'];
    final id = navigationData['id'];
    final action = navigationData['action'];
    
    if (type == null || action == null) return;
    
    if (kDebugMode) {
      print('Handling pending notification navigation - Type: $type, ID: $id, Action: $action');
    }
    
    // Wait a bit to ensure the app is fully loaded
    await Future.delayed(const Duration(milliseconds: 500));
    
    _navigateBasedOnNotification(type, id, action);
  }
  
  /// Navigate based on notification data
  static void _navigateBasedOnNotification(String type, String? id, String action) {
    final context = _navigatorKey?.currentContext;
    if (context == null) return;
    
    switch (type) {
      case AppConstants.bookingNotification:
        _handleBookingNotification(context, id, action);
        break;
        
      case AppConstants.reminderNotification:
        _handleReminderNotification(context, id, action);
        break;
        
      case AppConstants.promotionNotification:
        _handlePromotionNotification(context, id, action);
        break;
        
      case AppConstants.generalNotification:
        _handleGeneralNotification(context, action);
        break;
        
      default:
        // Navigate to home as fallback
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        break;
    }
  }
  
  /// Handle booking notification navigation
  static void _handleBookingNotification(BuildContext context, String? bookingId, String action) {
    switch (action) {
      case 'view_booking':
        if (bookingId != null) {
          // Navigate to specific booking details
          Navigator.of(context).pushNamedAndRemoveUntil(
            AppRoutes.main,
            (route) => false,
          );
          // Then navigate to booking history and show specific booking
          Future.delayed(const Duration(milliseconds: 300), () {
            Navigator.of(context).pushNamed(AppRoutes.bookingHistory);
          });
        }
        break;
        
      case 'book_again':
        // Navigate to services to book again
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        // Navigate to services tab
        break;
        
      default:
        // Navigate to booking history
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(AppRoutes.bookingHistory);
        });
        break;
    }
  }
  
  /// Handle reminder notification navigation
  static void _handleReminderNotification(BuildContext context, String? bookingId, String action) {
    switch (action) {
      case 'view_booking':
        if (bookingId != null) {
          // Navigate to specific booking details
          Navigator.of(context).pushNamedAndRemoveUntil(
            AppRoutes.main,
            (route) => false,
          );
          Future.delayed(const Duration(milliseconds: 300), () {
            Navigator.of(context).pushNamed(AppRoutes.bookingHistory);
          });
        }
        break;
        
      case 'reschedule':
        // Navigate to booking reschedule
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(AppRoutes.bookingHistory);
        });
        break;
        
      default:
        // Navigate to upcoming bookings
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(AppRoutes.bookingHistory);
        });
        break;
    }
  }
  
  /// Handle promotion notification navigation
  static void _handlePromotionNotification(BuildContext context, String? offerId, String action) {
    switch (action) {
      case 'view_offer':
        if (offerId != null && offerId != 'general') {
          // Navigate to specific offer
          Navigator.of(context).pushNamedAndRemoveUntil(
            AppRoutes.main,
            (route) => false,
          );
          Future.delayed(const Duration(milliseconds: 300), () {
            Navigator.of(context).pushNamed(AppRoutes.offers);
          });
        } else {
          _navigateToOffers(context);
        }
        break;
        
      case 'view_offers':
        _navigateToOffers(context);
        break;
        
      case 'book_service':
        // Navigate to services
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        // Services tab will be shown by default
        break;
        
      default:
        _navigateToOffers(context);
        break;
    }
  }
  
  /// Handle general notification navigation
  static void _handleGeneralNotification(BuildContext context, String action) {
    switch (action) {
      case 'view_gallery':
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(AppRoutes.gallery);
        });
        break;
        
      case 'view_services':
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        // Services tab will be shown by default
        break;
        
      case 'contact_us':
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(AppRoutes.contact);
        });
        break;
        
      default:
        // Navigate to home
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.main,
          (route) => false,
        );
        break;
    }
  }
  
  /// Helper method to navigate to offers
  static void _navigateToOffers(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      AppRoutes.main,
      (route) => false,
    );
    Future.delayed(const Duration(milliseconds: 300), () {
      Navigator.of(context).pushNamed(AppRoutes.offers);
    });
  }
  
  /// Show notification action dialog
  static void showNotificationDialog(BuildContext context, {
    required String title,
    required String message,
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onPrimaryPressed,
    VoidCallback? onSecondaryPressed,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          if (secondaryAction != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onSecondaryPressed?.call();
              },
              child: Text(secondaryAction),
            ),
          if (primaryAction != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onPrimaryPressed?.call();
              },
              child: Text(primaryAction),
            ),
        ],
      ),
    );
  }
}
