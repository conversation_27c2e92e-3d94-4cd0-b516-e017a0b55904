import 'package:hive/hive.dart';

part 'booking.g.dart';

@HiveType(typeId: 5)
class Booking extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String customerId;

  @HiveField(2)
  final String serviceId;

  @HiveField(3)
  final String? staffId;

  @HiveField(4)
  final DateTime bookingDate;

  @HiveField(5)
  final String timeSlot;

  @HiveField(6)
  final BookingStatus status;

  @HiveField(7)
  final double totalAmount;

  @HiveField(8)
  final String? notes;

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  final DateTime updatedAt;

  @HiveField(11)
  final String? cancellationReason;

  @HiveField(12)
  final DateTime? cancelledAt;

  @HiveField(13)
  final String? paymentMethod;

  @HiveField(14)
  final String? paymentStatus;

  @HiveField(15)
  final String? serviceName;

  @HiveField(16)
  final String? staffName;

  @HiveField(17)
  final int? duration;

  Booking({
    required this.id,
    required this.customerId,
    required this.serviceId,
    this.staffId,
    required this.bookingDate,
    required this.timeSlot,
    required this.status,
    required this.totalAmount,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.cancellationReason,
    this.cancelledAt,
    this.paymentMethod,
    this.paymentStatus,
    this.serviceName,
    this.staffName,
    this.duration,
  });

  factory Booking.fromJson(Map<String, dynamic> json) {
    return Booking(
      id: json['id'] ?? '',
      customerId: json['customer_id'] ?? '',
      serviceId: json['service_id'] ?? '',
      staffId: json['staff_id'],
      bookingDate: DateTime.parse(json['booking_date'] ?? DateTime.now().toIso8601String()),
      timeSlot: json['time_slot'] ?? '',
      status: BookingStatus.fromString(json['status'] ?? 'PENDING'),
      totalAmount: double.tryParse(json['total_amount'].toString()) ?? 0.0,
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      cancellationReason: json['cancellation_reason'],
      cancelledAt: json['cancelled_at'] != null 
          ? DateTime.parse(json['cancelled_at']) 
          : null,
      paymentMethod: json['payment_method'],
      paymentStatus: json['payment_status'],
      serviceName: json['service_name'],
      staffName: json['staff_name'],
      duration: json['duration'] != null 
          ? int.tryParse(json['duration'].toString()) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'service_id': serviceId,
      'staff_id': staffId,
      'booking_date': bookingDate.toIso8601String(),
      'time_slot': timeSlot,
      'status': status.value,
      'total_amount': totalAmount,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'cancellation_reason': cancellationReason,
      'cancelled_at': cancelledAt?.toIso8601String(),
      'payment_method': paymentMethod,
      'payment_status': paymentStatus,
      'service_name': serviceName,
      'staff_name': staffName,
      'duration': duration,
    };
  }

  Booking copyWith({
    String? id,
    String? customerId,
    String? serviceId,
    String? staffId,
    DateTime? bookingDate,
    String? timeSlot,
    BookingStatus? status,
    double? totalAmount,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? cancellationReason,
    DateTime? cancelledAt,
    String? paymentMethod,
    String? paymentStatus,
    String? serviceName,
    String? staffName,
    int? duration,
  }) {
    return Booking(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      serviceId: serviceId ?? this.serviceId,
      staffId: staffId ?? this.staffId,
      bookingDate: bookingDate ?? this.bookingDate,
      timeSlot: timeSlot ?? this.timeSlot,
      status: status ?? this.status,
      totalAmount: totalAmount ?? this.totalAmount,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      serviceName: serviceName ?? this.serviceName,
      staffName: staffName ?? this.staffName,
      duration: duration ?? this.duration,
    );
  }

  String get formattedAmount => 'TSH ${totalAmount.toStringAsFixed(0)}';
  String get formattedDate => '${bookingDate.day}/${bookingDate.month}/${bookingDate.year}';
  String get formattedDateTime => '$formattedDate at $timeSlot';
  
  bool get canCancel {
    if (status == BookingStatus.cancelled || status == BookingStatus.completed) {
      return false;
    }
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      bookingDate.year,
      bookingDate.month,
      bookingDate.day,
      int.parse(timeSlot.split(':')[0]),
      int.parse(timeSlot.split(':')[1]),
    );
    return bookingDateTime.difference(now).inHours >= 24;
  }

  bool get isUpcoming {
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      bookingDate.year,
      bookingDate.month,
      bookingDate.day,
      int.parse(timeSlot.split(':')[0]),
      int.parse(timeSlot.split(':')[1]),
    );
    return bookingDateTime.isAfter(now) && 
           (status == BookingStatus.pending || status == BookingStatus.confirmed);
  }

  bool get isPast {
    final now = DateTime.now();
    final bookingDateTime = DateTime(
      bookingDate.year,
      bookingDate.month,
      bookingDate.day,
      int.parse(timeSlot.split(':')[0]),
      int.parse(timeSlot.split(':')[1]),
    );
    return bookingDateTime.isBefore(now);
  }
}

@HiveType(typeId: 6)
enum BookingStatus {
  @HiveField(0)
  pending('PENDING'),
  @HiveField(1)
  confirmed('CONFIRMED'),
  @HiveField(2)
  inProgress('IN_PROGRESS'),
  @HiveField(3)
  completed('COMPLETED'),
  @HiveField(4)
  cancelled('CANCELLED'),
  @HiveField(5)
  noShow('NO_SHOW'),
  @HiveField(6)
  expired('EXPIRED');

  const BookingStatus(this.value);
  final String value;

  static BookingStatus fromString(String value) {
    switch (value.toUpperCase()) {
      case 'PENDING':
        return BookingStatus.pending;
      case 'CONFIRMED':
        return BookingStatus.confirmed;
      case 'IN_PROGRESS':
        return BookingStatus.inProgress;
      case 'COMPLETED':
        return BookingStatus.completed;
      case 'CANCELLED':
        return BookingStatus.cancelled;
      case 'NO_SHOW':
        return BookingStatus.noShow;
      case 'EXPIRED':
        return BookingStatus.expired;
      default:
        return BookingStatus.pending;
    }
  }

  String get displayName {
    switch (this) {
      case BookingStatus.pending:
        return 'Pending';
      case BookingStatus.confirmed:
        return 'Confirmed';
      case BookingStatus.inProgress:
        return 'In Progress';
      case BookingStatus.completed:
        return 'Completed';
      case BookingStatus.cancelled:
        return 'Cancelled';
      case BookingStatus.noShow:
        return 'No Show';
      case BookingStatus.expired:
        return 'Expired';
    }
  }

  @override
  String toString() => value;
}
