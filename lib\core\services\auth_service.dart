import 'dart:convert';
import '../config/app_config.dart';
import '../models/user.dart';
import '../utils/app_constants.dart';
import 'api_service.dart';
import 'storage_service.dart';

class AuthService {
  final ApiService _apiService;
  
  AuthService({required ApiService apiService}) : _apiService = apiService;
  
  // Login
  Future<ApiResponse<User>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/login.php',
        data: {
          'email': email,
          'password': password,
          'remember_me': rememberMe,
        },
      );
      
      if (response.isSuccess && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final token = data[AppConstants.tokenKey];
        final userData = data[AppConstants.userKey];
        
        if (token != null && userData != null) {
          // Save token and user data
          await StorageService.setString(AppConstants.userTokenKey, token);
          await StorageService.setBool(AppConstants.isLoggedInKey, true);
          await StorageService.setBool(AppConstants.rememberMeKey, rememberMe);
          
          final user = User.fromJson(userData);
          await StorageService.setString(
            AppConstants.userDataKey, 
            jsonEncode(user.toJson()),
          );
          await StorageService.setString(AppConstants.userIdKey, user.id);
          await StorageService.setString(AppConstants.userRoleKey, user.role.value);
          
          return ApiResponse.success(user, response.message);
        }
      }
      
      return ApiResponse.error(response.error ?? 'Login failed');
    } catch (e) {
      return ApiResponse.error('Login failed: $e');
    }
  }
  
  // Register
  Future<ApiResponse<User>> register({
    required String name,
    required String email,
    required String password,
    required String confirmPassword,
    String? phone,
    String? referralCode,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/register.php',
        data: {
          'name': name,
          'email': email,
          'password': password,
          'confirm_password': confirmPassword,
          'phone': phone,
          'referral_code': referralCode,
        },
      );
      
      if (response.isSuccess && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final userData = data[AppConstants.userKey];
        
        if (userData != null) {
          final user = User.fromJson(userData);
          return ApiResponse.success(user, response.message);
        }
      }
      
      return ApiResponse.error(response.error ?? 'Registration failed');
    } catch (e) {
      return ApiResponse.error('Registration failed: $e');
    }
  }
  
  // Forgot Password
  Future<ApiResponse<String>> forgotPassword({required String email}) async {
    try {
      final response = await _apiService.post(
        '/auth/forgot-password.php',
        data: {'email': email},
      );
      
      if (response.isSuccess) {
        return ApiResponse.success(
          response.data?.toString(),
          response.message,
        );
      }
      
      return ApiResponse.error(response.error ?? 'Failed to send reset email');
    } catch (e) {
      return ApiResponse.error('Failed to send reset email: $e');
    }
  }
  
  // Reset Password
  Future<ApiResponse<String>> resetPassword({
    required String token,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/reset-password.php',
        data: {
          'token': token,
          'password': password,
          'confirm_password': confirmPassword,
        },
      );
      
      if (response.isSuccess) {
        return ApiResponse.success(
          response.data?.toString(),
          response.message,
        );
      }
      
      return ApiResponse.error(response.error ?? 'Password reset failed');
    } catch (e) {
      return ApiResponse.error('Password reset failed: $e');
    }
  }
  
  // Verify OTP
  Future<ApiResponse<String>> verifyOtp({
    required String email,
    required String otp,
  }) async {
    try {
      final response = await _apiService.post(
        '/auth/verify-otp.php',
        data: {
          'email': email,
          'otp': otp,
        },
      );
      
      if (response.isSuccess) {
        return ApiResponse.success(
          response.data?.toString(),
          response.message,
        );
      }
      
      return ApiResponse.error(response.error ?? 'OTP verification failed');
    } catch (e) {
      return ApiResponse.error('OTP verification failed: $e');
    }
  }
  
  // Logout
  Future<ApiResponse<String>> logout() async {
    try {
      final response = await _apiService.post('/auth/logout.php');
      
      // Clear local storage regardless of API response
      await _clearLocalData();
      
      if (response.isSuccess) {
        return ApiResponse.success(
          response.data?.toString(),
          response.message,
        );
      }
      
      return ApiResponse.success(null, 'Logged out successfully');
    } catch (e) {
      // Clear local data even if API call fails
      await _clearLocalData();
      return ApiResponse.success(null, 'Logged out successfully');
    }
  }
  
  // Get current user
  Future<User?> getCurrentUser() async {
    try {
      final userDataString = await StorageService.getString(AppConstants.userDataKey);
      if (userDataString != null && userDataString.isNotEmpty) {
        final userData = jsonDecode(userDataString);
        return User.fromJson(userData);
      }
    } catch (e) {
      print('Error getting current user: $e');
    }
    return null;
  }
  
  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final isLoggedIn = await StorageService.getBool(AppConstants.isLoggedInKey);
    final token = await StorageService.getString(AppConstants.userTokenKey);
    return isLoggedIn == true && token != null && token.isNotEmpty;
  }
  
  // Get auth token
  Future<String?> getAuthToken() async {
    return await StorageService.getString(AppConstants.userTokenKey);
  }
  
  // Refresh user data
  Future<ApiResponse<User>> refreshUserData() async {
    try {
      final response = await _apiService.get('/auth/me.php');
      
      if (response.isSuccess && response.data != null) {
        final userData = response.data as Map<String, dynamic>;
        final user = User.fromJson(userData);
        
        // Update stored user data
        await StorageService.setString(
          AppConstants.userDataKey,
          jsonEncode(user.toJson()),
        );
        
        return ApiResponse.success(user, response.message);
      }
      
      return ApiResponse.error(response.error ?? 'Failed to refresh user data');
    } catch (e) {
      return ApiResponse.error('Failed to refresh user data: $e');
    }
  }
  
  // Update profile
  Future<ApiResponse<User>> updateProfile({
    String? name,
    String? phone,
    DateTime? dateOfBirth,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (phone != null) data['phone'] = phone;
      if (dateOfBirth != null) {
        data['date_of_birth'] = dateOfBirth.toIso8601String().split('T')[0];
      }
      
      final response = await _apiService.put('/auth/profile.php', data: data);
      
      if (response.isSuccess && response.data != null) {
        final userData = response.data as Map<String, dynamic>;
        final user = User.fromJson(userData);
        
        // Update stored user data
        await StorageService.setString(
          AppConstants.userDataKey,
          jsonEncode(user.toJson()),
        );
        
        return ApiResponse.success(user, response.message);
      }
      
      return ApiResponse.error(response.error ?? 'Profile update failed');
    } catch (e) {
      return ApiResponse.error('Profile update failed: $e');
    }
  }
  
  // Change password
  Future<ApiResponse<String>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final response = await _apiService.put(
        '/auth/change-password.php',
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
          'confirm_password': confirmPassword,
        },
      );
      
      if (response.isSuccess) {
        return ApiResponse.success(
          response.data?.toString(),
          response.message,
        );
      }
      
      return ApiResponse.error(response.error ?? 'Password change failed');
    } catch (e) {
      return ApiResponse.error('Password change failed: $e');
    }
  }
  
  // Clear local authentication data
  Future<void> _clearLocalData() async {
    await StorageService.remove(AppConstants.userTokenKey);
    await StorageService.remove(AppConstants.userDataKey);
    await StorageService.remove(AppConstants.userIdKey);
    await StorageService.remove(AppConstants.userRoleKey);
    await StorageService.setBool(AppConstants.isLoggedInKey, false);
  }
}
