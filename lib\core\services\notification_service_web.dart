// Web stub for Firebase messaging (not supported on web)
class FirebaseMessaging {
  static FirebaseMessaging get instance => FirebaseMessaging._();
  
  FirebaseMessaging._();
  
  Future<NotificationSettings> requestPermission({
    bool alert = true,
    bool announcement = false,
    bool badge = true,
    bool carPlay = false,
    bool criticalAlert = false,
    bool provisional = false,
    bool sound = true,
  }) async {
    return NotificationSettings(
      alert: AppleNotificationSetting.enabled,
      announcement: AppleNotificationSetting.notSupported,
      authorizationStatus: AuthorizationStatus.authorized,
      badge: AppleNotificationSetting.enabled,
      carPlay: AppleNotificationSetting.notSupported,
      criticalAlert: AppleNotificationSetting.notSupported,
      lockScreen: AppleNotificationSetting.enabled,
      notificationCenter: AppleNotificationSetting.enabled,
      showPreviews: AppleShowPreviewSetting.always,
      sound: AppleNotificationSetting.enabled,
      timeSensitive: AppleNotificationSetting.notSupported,
    );
  }
  
  Future<RemoteMessage?> getInitialMessage() async => null;
  
  Future<String?> getToken() async => null;
  
  Future<void> subscribeToTopic(String topic) async {}
  
  Future<void> unsubscribeFromTopic(String topic) async {}
  
  Stream<RemoteMessage> get onMessage => const Stream.empty();
  
  static Stream<RemoteMessage> get onMessageOpenedApp => const Stream.empty();
  
  static Stream<RemoteMessage> get onBackgroundMessage => const Stream.empty();
}

// Stub classes for web compatibility
class NotificationSettings {
  final AppleNotificationSetting alert;
  final AppleNotificationSetting announcement;
  final AuthorizationStatus authorizationStatus;
  final AppleNotificationSetting badge;
  final AppleNotificationSetting carPlay;
  final AppleNotificationSetting criticalAlert;
  final AppleNotificationSetting lockScreen;
  final AppleNotificationSetting notificationCenter;
  final AppleShowPreviewSetting showPreviews;
  final AppleNotificationSetting sound;
  final AppleNotificationSetting timeSensitive;
  
  const NotificationSettings({
    required this.alert,
    required this.announcement,
    required this.authorizationStatus,
    required this.badge,
    required this.carPlay,
    required this.criticalAlert,
    required this.lockScreen,
    required this.notificationCenter,
    required this.showPreviews,
    required this.sound,
    required this.timeSensitive,
  });
}

enum AppleNotificationSetting { enabled, disabled, notSupported }
enum AuthorizationStatus { authorized, denied, notDetermined, provisional }
enum AppleShowPreviewSetting { always, never, whenAuthenticated }

class RemoteMessage {
  final String? messageId;
  final Map<String, dynamic> data;
  final RemoteNotification? notification;

  const RemoteMessage({
    this.messageId,
    this.data = const <String, dynamic>{},
    this.notification,
  });
}

class RemoteNotification {
  final String? title;
  final String? body;
  
  const RemoteNotification({
    this.title,
    this.body,
  });
}
