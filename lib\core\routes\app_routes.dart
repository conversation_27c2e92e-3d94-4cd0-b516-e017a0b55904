import 'package:flutter/material.dart';

import '../../features/splash/screens/splash_screen.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/reset_password_screen.dart';
import '../../features/auth/screens/verify_otp_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/main/screens/main_screen.dart';
import '../../features/services/screens/services_screen.dart';
import '../../features/services/screens/service_details_screen.dart';
import '../../features/booking/screens/booking_screen.dart';
import '../../features/booking/screens/booking_confirmation_screen.dart';
import '../../features/booking/screens/booking_details_screen.dart';
// import '../../features/dashboard/screens/dashboard_screen.dart'; // Unused
import '../../features/dashboard/screens/booking_history_screen.dart';
import '../../features/dashboard/screens/profile_screen.dart';
import '../../features/profile/screens/edit_profile_screen.dart';
import '../../features/gallery/screens/gallery_screen.dart';
import '../../features/offers/screens/offers_screen.dart';
import '../../features/blog/screens/blog_screen.dart';

import '../../features/contact/screens/contact_screen.dart';
import '../../features/about/screens/about_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String verifyOtp = '/verify-otp';
  static const String home = '/home';
  static const String main = '/main';
  static const String services = '/services';
  static const String serviceDetails = '/service-details';
  static const String booking = '/booking';
  static const String bookingConfirmation = '/booking-confirmation';
  static const String bookingDetails = '/booking-details';
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String changePassword = '/change-password';
  static const String bookingHistory = '/booking-history';
  static const String gallery = '/gallery';
  static const String offers = '/offers';
  static const String blog = '/blog';
  static const String blogDetails = '/blog-details';
  static const String contact = '/contact';
  static const String about = '/about';
  
  // Generate routes
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
          settings: settings,
        );
        
      case onboarding:
        return MaterialPageRoute(
          builder: (_) => const OnboardingScreen(),
          settings: settings,
        );
        
      case login:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
          settings: settings,
        );
        
      case register:
        return MaterialPageRoute(
          builder: (_) => const RegisterScreen(),
          settings: settings,
        );
        
      case forgotPassword:
        return MaterialPageRoute(
          builder: (_) => const ForgotPasswordScreen(),
          settings: settings,
        );
        
      case resetPassword:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ResetPasswordScreen(
            token: args?['token'] ?? '',
          ),
          settings: settings,
        );
        
      case verifyOtp:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => VerifyOtpScreen(
            email: args?['email'] ?? '',
            purpose: args?['purpose'] ?? 'verification',
          ),
          settings: settings,
        );

      case main:
        return MaterialPageRoute(
          builder: (_) => const MainScreen(),
          settings: settings,
        );

      case home:
        return MaterialPageRoute(
          builder: (_) => const HomeScreen(),
          settings: settings,
        );

      case services:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ServicesScreen(
            category: args?['category'],
            subcategory: args?['subcategory'],
          ),
          settings: settings,
        );

      case serviceDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ServiceDetailsScreen(
            serviceId: args?['serviceId'] ?? '',
          ),
          settings: settings,
        );

      case booking:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => BookingScreen(
            serviceId: args?['serviceId'],
            packageId: args?['packageId'],
          ),
          settings: settings,
        );

      case bookingHistory:
        return MaterialPageRoute(
          builder: (_) => const BookingHistoryScreen(),
          settings: settings,
        );

      case bookingDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => BookingDetailsScreen(
            bookingId: args?['bookingId'] ?? '',
          ),
          settings: settings,
        );

      case bookingConfirmation:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => BookingConfirmationScreen(
            bookingData: args ?? {},
          ),
          settings: settings,
        );

      case profile:
        return MaterialPageRoute(
          builder: (_) => const ProfileScreen(),
          settings: settings,
        );

      case editProfile:
        return MaterialPageRoute(
          builder: (_) => const EditProfileScreen(),
          settings: settings,
        );

      case gallery:
        return MaterialPageRoute(
          builder: (_) => const GalleryScreen(),
          settings: settings,
        );

      case offers:
        return MaterialPageRoute(
          builder: (_) => const OffersScreen(),
          settings: settings,
        );

      case blog:
        return MaterialPageRoute(
          builder: (_) => const BlogScreen(),
          settings: settings,
        );

      case contact:
        return MaterialPageRoute(
          builder: (_) => const ContactScreen(),
          settings: settings,
        );

      case about:
        return MaterialPageRoute(
          builder: (_) => const AboutScreen(),
          settings: settings,
        );

        
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Text('Page not found'),
            ),
          ),
          settings: settings,
        );
    }
  }
  
  // Navigation helpers
  static Future<T?> pushNamed<T extends Object?>(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    return Navigator.pushNamed<T>(
      context,
      routeName,
      arguments: arguments,
    );
  }
  
  static Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    BuildContext context,
    String routeName, {
    Object? arguments,
    TO? result,
  }) {
    return Navigator.pushReplacementNamed<T, TO>(
      context,
      routeName,
      arguments: arguments,
      result: result,
    );
  }
  
  static Future<T?> pushNamedAndRemoveUntil<T extends Object?>(
    BuildContext context,
    String routeName,
    RoutePredicate predicate, {
    Object? arguments,
  }) {
    return Navigator.pushNamedAndRemoveUntil<T>(
      context,
      routeName,
      predicate,
      arguments: arguments,
    );
  }
  
  static void pop<T extends Object?>(BuildContext context, [T? result]) {
    Navigator.pop<T>(context, result);
  }
  
  static void popUntil(BuildContext context, RoutePredicate predicate) {
    Navigator.popUntil(context, predicate);
  }
  
  static bool canPop(BuildContext context) {
    return Navigator.canPop(context);
  }
}
