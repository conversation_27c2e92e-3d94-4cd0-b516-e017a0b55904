import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/utils/app_constants.dart';
import '../widgets/hero_section.dart';
import '../widgets/service_category_card.dart';
import '../widgets/featured_service_card.dart';
import '../widgets/testimonial_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final CarouselController _carouselController = CarouselController();
  int _currentCarouselIndex = 0;

  final List<String> _heroImages = [
    'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=800',
    'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=800',
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
  ];

  final List<Map<String, dynamic>> _serviceCategories = [
    {
      'title': 'Hair Care',
      'icon': Icons.content_cut,
      'color': ThemeConfig.primaryColor,
      'category': 'hair',
    },
    {
      'title': 'Skin Care',
      'icon': Icons.face,
      'color': ThemeConfig.secondaryColor,
      'category': 'skin',
    },
    {
      'title': 'Nail Care',
      'icon': Icons.back_hand,
      'color': ThemeConfig.accentColor,
      'category': 'nail',
    },
    {
      'title': 'Massage',
      'icon': Icons.spa,
      'color': Colors.purple,
      'category': 'massage',
    },
  ];

  final List<Map<String, dynamic>> _featuredServices = [
    {
      'name': 'Premium Facial',
      'price': 150.0,
      'duration': 60,
      'image': 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400',
      'rating': 4.8,
    },
    {
      'name': 'Hair Styling',
      'price': 80.0,
      'duration': 45,
      'image': 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400',
      'rating': 4.9,
    },
    {
      'name': 'Manicure & Pedicure',
      'price': 120.0,
      'duration': 90,
      'image': 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400',
      'rating': 4.7,
    },
  ];

  final List<Map<String, dynamic>> _testimonials = [
    {
      'name': 'Sarah Johnson',
      'rating': 5.0,
      'comment': 'Amazing service! The staff is professional and the ambiance is perfect.',
      'image': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200',
    },
    {
      'name': 'Emily Davis',
      'rating': 4.8,
      'comment': 'Love coming here! Always leave feeling refreshed and beautiful.',
      'image': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.menu, color: ThemeConfig.textPrimary),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        title: const Text(
          AppConstants.appName,
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: ThemeConfig.textPrimary),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero Section
            HeroSection(
              images: _heroImages,
              onImageChanged: (index) {
                setState(() {
                  _currentCarouselIndex = index;
                });
              },
            ),
            
            const SizedBox(height: 32),
            
            // Service Categories
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Our Services',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: ThemeConfig.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: _serviceCategories.length,
                    itemBuilder: (context, index) {
                      final category = _serviceCategories[index];
                      return ServiceCategoryCard(
                        title: category['title'],
                        icon: category['icon'],
                        color: category['color'],
                        onTap: () {
                          AppRoutes.pushNamed(
                            context,
                            AppRoutes.services,
                            arguments: {'category': category['category']},
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Featured Services
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24),
                  child: Text(
                    'Featured Services',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: ThemeConfig.textPrimary,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 280,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    itemCount: _featuredServices.length,
                    itemBuilder: (context, index) {
                      final service = _featuredServices[index];
                      return Padding(
                        padding: EdgeInsets.only(
                          right: index < _featuredServices.length - 1 ? 16 : 0,
                        ),
                        child: FeaturedServiceCard(
                          name: service['name'],
                          price: service['price'],
                          duration: service['duration'],
                          image: service['image'],
                          rating: service['rating'],
                          onTap: () {
                            AppRoutes.pushNamed(
                              context,
                              AppRoutes.serviceDetails,
                              arguments: {'serviceId': 'service_$index'},
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Testimonials
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24),
                  child: Text(
                    'What Our Clients Say',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: ThemeConfig.textPrimary,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    itemCount: _testimonials.length,
                    itemBuilder: (context, index) {
                      final testimonial = _testimonials[index];
                      return Padding(
                        padding: EdgeInsets.only(
                          right: index < _testimonials.length - 1 ? 16 : 0,
                        ),
                        child: TestimonialCard(
                          name: testimonial['name'],
                          rating: testimonial['rating'],
                          comment: testimonial['comment'],
                          image: testimonial['image'],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Call to Action
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: ThemeConfig.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  const Text(
                    'Ready to Book?',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Experience luxury beauty services today',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        AppRoutes.pushNamed(context, AppRoutes.booking);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: ThemeConfig.primaryColor,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Book Now',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
