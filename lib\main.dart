import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
// import 'package:firebase_core/firebase_core.dart'; // Commented out for now

import 'core/config/theme_config.dart';
import 'core/services/api_service.dart';
import 'core/services/auth_service.dart';
import 'core/services/notification_service.dart';
import 'core/services/storage_service.dart';
import 'features/auth/providers/auth_provider.dart';
import 'core/routes/app_routes.dart';
import 'core/utils/app_constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('Starting Flix App...');
  }

  try {
    // Skip Firebase initialization for now
    // if (!kIsWeb) {
    //   try {
    //     await Firebase.initializeApp();
    //   } catch (e) {
    //     if (kDebugMode) {
    //       print('Firebase initialization failed: $e');
    //     }
    //   }
    // }

    // Initialize Hive
    await Hive.initFlutter();
    if (kDebugMode) {
      print('Hive initialized');
    }

    // Initialize services
    await StorageService.init();
    if (kDebugMode) {
      print('StorageService initialized');
    }

    await NotificationService.init();
    if (kDebugMode) {
      print('NotificationService initialized');
    }

    // Set preferred orientations (skip on web)
    if (!kIsWeb) {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      // Set system UI overlay style
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      );
    }

    if (kDebugMode) {
      print('All services initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Initialization error: $e');
    }
  }

  runApp(const FlixApp());
}

class FlixApp extends StatefulWidget {
  const FlixApp({super.key});

  @override
  State<FlixApp> createState() => _FlixAppState();
}

class _FlixAppState extends State<FlixApp> {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Services
        Provider<ApiService>(
          create: (_) => ApiService(),
        ),
        Provider<AuthService>(
          create: (context) => AuthService(
            apiService: context.read<ApiService>(),
          ),
        ),

        // Providers
        ChangeNotifierProvider<AuthProvider>(
          create: (context) => AuthProvider(
            authService: context.read<AuthService>(),
          ),
        ),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (kDebugMode) {
            print('AuthProvider state - isLoading: ${authProvider.isLoading}, isAuthenticated: ${authProvider.isAuthenticated}');
          }

          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            theme: ThemeConfig.lightTheme,
            darkTheme: ThemeConfig.darkTheme,
            themeMode: ThemeMode.light,
            initialRoute: AppRoutes.splash,
            onGenerateRoute: AppRoutes.generateRoute,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1.0),
                ),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
