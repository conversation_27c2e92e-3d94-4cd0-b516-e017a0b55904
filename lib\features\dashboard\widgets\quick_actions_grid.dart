import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';

class QuickActionsGrid extends StatelessWidget {
  final VoidCallback onBookNow;
  final VoidCallback onViewBookings;
  final VoidCallback onViewOffers;
  final VoidCallback onContactUs;

  const QuickActionsGrid({
    super.key,
    required this.onBookNow,
    required this.onViewBookings,
    required this.onViewOffers,
    required this.onContactUs,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: ThemeConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildActionCard(
              icon: Icons.calendar_today,
              title: 'Book Now',
              subtitle: 'Schedule appointment',
              color: ThemeConfig.primaryColor,
              onTap: onBookNow,
            ),
            _buildActionCard(
              icon: Icons.history,
              title: 'My Bookings',
              subtitle: 'View booking history',
              color: ThemeConfig.secondaryColor,
              onTap: onViewBookings,
            ),
            _buildActionCard(
              icon: Icons.local_offer,
              title: 'Offers',
              subtitle: 'Special deals',
              color: ThemeConfig.accentColor,
              onTap: onViewOffers,
            ),
            _buildActionCard(
              icon: Icons.support_agent,
              title: 'Contact Us',
              subtitle: 'Get support',
              color: Colors.teal,
              onTap: onContactUs,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ThemeConfig.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
                color: ThemeConfig.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
