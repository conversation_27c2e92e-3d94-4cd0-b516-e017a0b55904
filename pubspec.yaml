name: flix_app
description: Flix Salon & SPA - Customer Mobile Application
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.1.1
  lottie: ^2.7.0
  
  # State Management
  provider: ^6.1.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Authentication & Security
  crypto: ^3.0.3
  
  # Date & Time
  intl: 0.20.2
  
  # Image Handling
  image_picker: ^1.0.4
  
  # Push Notifications (mobile only)
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  
  # URL Launcher
  url_launcher: ^6.2.1
  
  # Permissions
  permission_handler: ^11.0.1
  
  # Utils
  uuid: ^4.1.0
  flutter_rating_bar: ^4.0.1
  carousel_slider: ^5.1.1
  smooth_page_indicator: ^1.2.1
  pin_code_fields: ^8.0.1
  table_calendar: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
