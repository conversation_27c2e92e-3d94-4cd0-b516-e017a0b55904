import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/utils/app_constants.dart';
import '../../auth/providers/auth_provider.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      child: Column(
        children: [
          // Header
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              final user = authProvider.currentUser;
              return UserAccountsDrawerHeader(
                decoration: const BoxDecoration(
                  gradient: ThemeConfig.primaryGradient,
                ),
                accountName: Text(
                  user?.name ?? 'Guest User',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                accountEmail: Text(
                  user?.email ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundColor: Colors.white,
                  child: user?.profileImage != null
                      ? ClipOval(
                          child: Image.network(
                            user!.profileImage!,
                            width: 70,
                            height: 70,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Text(
                                user.initials,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: ThemeConfig.primaryColor,
                                ),
                              );
                            },
                          ),
                        )
                      : Text(
                          user?.initials ?? 'G',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: ThemeConfig.primaryColor,
                          ),
                        ),
                ),
              );
            },
          ),
          
          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: Icons.home,
                  title: 'Home',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamedAndRemoveUntil(
                      context,
                      AppRoutes.main,
                      (route) => false,
                    );
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.spa,
                  title: 'Services',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.services);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.calendar_today,
                  title: 'My Bookings',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.bookingHistory);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.photo_library,
                  title: 'Gallery',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.gallery);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.local_offer,
                  title: 'Offers',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.offers);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.article,
                  title: 'Blog',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.blog);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.info,
                  title: 'About Us',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.about);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.contact_phone,
                  title: 'Contact',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.contact);
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: Icons.person,
                  title: 'Profile',
                  onTap: () {
                    Navigator.pop(context);
                    AppRoutes.pushNamed(context, AppRoutes.profile);
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.settings,
                  title: 'Settings',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Navigate to settings
                  },
                ),
              ],
            ),
          ),
          
          // Footer
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.logout,
            title: 'Logout',
            textColor: ThemeConfig.errorColor,
            onTap: () async {
              Navigator.pop(context);
              await _showLogoutDialog(context);
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? ThemeConfig.textSecondary,
        size: 24,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: textColor ?? ThemeConfig.textPrimary,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 24,
        vertical: 4,
      ),
    );
  }

  Future<void> _showLogoutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: ThemeConfig.errorColor,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      final authProvider = context.read<AuthProvider>();
      await authProvider.logout();
      
      if (context.mounted) {
        AppRoutes.pushNamedAndRemoveUntil(
          context,
          AppRoutes.login,
          (route) => false,
        );
      }
    }
  }
}
