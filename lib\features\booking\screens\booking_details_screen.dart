import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';

class BookingDetailsScreen extends StatelessWidget {
  final String bookingId;

  const BookingDetailsScreen({
    super.key,
    required this.bookingId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Booking Details',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: ThemeConfig.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Center(
        child: Text(
          'Booking Details Screen\nBooking ID: $bookingId\n(To be implemented)',
          style: const TextStyle(
            fontSize: 18,
            color: ThemeConfig.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
