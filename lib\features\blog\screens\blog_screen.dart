import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';

class BlogScreen extends StatelessWidget {
  const BlogScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Blog',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: ThemeConfig.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: const Center(
        child: Text(
          'Blog Screen\n(To be implemented)',
          style: TextStyle(
            fontSize: 18,
            color: ThemeConfig.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
