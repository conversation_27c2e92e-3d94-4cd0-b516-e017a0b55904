import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static SharedPreferences? _prefs;
  
  // Initialize storage service
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  // Ensure preferences are initialized
  static Future<SharedPreferences> get _preferences async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }
  
  // String operations
  static Future<bool> setString(String key, String value) async {
    final prefs = await _preferences;
    return await prefs.setString(key, value);
  }
  
  static Future<String?> getString(String key) async {
    final prefs = await _preferences;
    return prefs.getString(key);
  }
  
  // Integer operations
  static Future<bool> setInt(String key, int value) async {
    final prefs = await _preferences;
    return await prefs.setInt(key, value);
  }
  
  static Future<int?> getInt(String key) async {
    final prefs = await _preferences;
    return prefs.getInt(key);
  }
  
  // Double operations
  static Future<bool> setDouble(String key, double value) async {
    final prefs = await _preferences;
    return await prefs.setDouble(key, value);
  }
  
  static Future<double?> getDouble(String key) async {
    final prefs = await _preferences;
    return prefs.getDouble(key);
  }
  
  // Boolean operations
  static Future<bool> setBool(String key, bool value) async {
    final prefs = await _preferences;
    return await prefs.setBool(key, value);
  }
  
  static Future<bool?> getBool(String key) async {
    final prefs = await _preferences;
    return prefs.getBool(key);
  }
  
  // String list operations
  static Future<bool> setStringList(String key, List<String> value) async {
    final prefs = await _preferences;
    return await prefs.setStringList(key, value);
  }
  
  static Future<List<String>?> getStringList(String key) async {
    final prefs = await _preferences;
    return prefs.getStringList(key);
  }
  
  // Remove key
  static Future<bool> remove(String key) async {
    final prefs = await _preferences;
    return await prefs.remove(key);
  }
  
  // Clear all data
  static Future<bool> clear() async {
    final prefs = await _preferences;
    return await prefs.clear();
  }
  
  // Check if key exists
  static Future<bool> containsKey(String key) async {
    final prefs = await _preferences;
    return prefs.containsKey(key);
  }
  
  // Get all keys
  static Future<Set<String>> getKeys() async {
    final prefs = await _preferences;
    return prefs.getKeys();
  }
}
