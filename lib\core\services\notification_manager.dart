import 'package:flutter/foundation.dart';

import '../utils/app_constants.dart';
import 'notification_service.dart';
import 'storage_service.dart';

class NotificationManager {
  // Booking notification IDs start from 1000
  static const int _bookingNotificationIdBase = 1000;
  
  // Reminder notification IDs start from 2000
  static const int _reminderNotificationIdBase = 2000;
  
  // Promotion notification IDs start from 3000
  static const int _promotionNotificationIdBase = 3000;

  /// Schedule booking confirmation notification
  static Future<void> scheduleBookingConfirmation({
    required String bookingId,
    required String serviceName,
    required DateTime appointmentDate,
    required String customerName,
  }) async {
    if (!await _shouldSendNotification()) return;

    final notificationId = _bookingNotificationIdBase + bookingId.hashCode.abs() % 1000;
    
    await NotificationService.scheduleNotification(
      id: notificationId,
      title: 'Booking Confirmed! 🎉',
      body: 'Your $serviceName appointment is confirmed for ${_formatDate(appointmentDate)}',
      scheduledDate: DateTime.now().add(const Duration(seconds: 5)), // Immediate confirmation
      payload: _createPayload(
        type: AppConstants.bookingNotification,
        id: bookingId,
        action: 'view_booking',
      ),
    );

    if (kDebugMode) {
      print('Scheduled booking confirmation for booking: $bookingId');
    }
  }

  /// Schedule booking reminder notifications
  static Future<void> scheduleBookingReminders({
    required String bookingId,
    required String serviceName,
    required DateTime appointmentDate,
    required String customerName,
  }) async {
    if (!await _shouldSendNotification()) return;

    final baseId = _reminderNotificationIdBase + bookingId.hashCode.abs() % 1000;
    
    // 24-hour reminder
    final oneDayBefore = appointmentDate.subtract(const Duration(days: 1));
    if (oneDayBefore.isAfter(DateTime.now())) {
      await NotificationService.scheduleNotification(
        id: baseId,
        title: 'Appointment Tomorrow 📅',
        body: 'Don\'t forget your $serviceName appointment at ${_formatTime(appointmentDate)}',
        scheduledDate: oneDayBefore,
        payload: _createPayload(
          type: AppConstants.reminderNotification,
          id: bookingId,
          action: 'view_booking',
        ),
      );
    }

    // 2-hour reminder
    final twoHoursBefore = appointmentDate.subtract(const Duration(hours: 2));
    if (twoHoursBefore.isAfter(DateTime.now())) {
      await NotificationService.scheduleNotification(
        id: baseId + 1,
        title: 'Appointment in 2 Hours ⏰',
        body: 'Your $serviceName appointment is coming up soon. See you at Flix Salon & SPA!',
        scheduledDate: twoHoursBefore,
        payload: _createPayload(
          type: AppConstants.reminderNotification,
          id: bookingId,
          action: 'view_booking',
        ),
      );
    }

    if (kDebugMode) {
      print('Scheduled reminders for booking: $bookingId');
    }
  }

  /// Schedule promotional notification
  static Future<void> schedulePromotionalNotification({
    required String offerId,
    required String title,
    required String message,
    required DateTime scheduledDate,
    String? imageUrl,
  }) async {
    if (!await _shouldSendNotification()) return;

    final notificationId = _promotionNotificationIdBase + offerId.hashCode.abs() % 1000;
    
    await NotificationService.scheduleNotification(
      id: notificationId,
      title: title,
      body: message,
      scheduledDate: scheduledDate,
      payload: _createPayload(
        type: AppConstants.promotionNotification,
        id: offerId,
        action: 'view_offer',
        extra: imageUrl,
      ),
    );

    if (kDebugMode) {
      print('Scheduled promotional notification for offer: $offerId');
    }
  }

  /// Send immediate promotional notification
  static Future<void> sendPromotionalNotification({
    required String title,
    required String message,
    String? offerId,
    String? imageUrl,
  }) async {
    if (!await _shouldSendNotification()) return;

    final notificationId = _promotionNotificationIdBase + DateTime.now().millisecondsSinceEpoch % 1000;
    
    await NotificationService.scheduleNotification(
      id: notificationId,
      title: title,
      body: message,
      scheduledDate: DateTime.now().add(const Duration(seconds: 1)),
      payload: _createPayload(
        type: AppConstants.promotionNotification,
        id: offerId ?? 'general',
        action: offerId != null ? 'view_offer' : 'view_offers',
        extra: imageUrl,
      ),
    );

    if (kDebugMode) {
      print('Sent promotional notification: $title');
    }
  }

  /// Schedule birthday/anniversary notification
  static Future<void> scheduleSpecialOccasionNotification({
    required String customerId,
    required String customerName,
    required String occasion, // 'birthday' or 'anniversary'
    required DateTime occasionDate,
  }) async {
    if (!await _shouldSendNotification()) return;

    final notificationId = _promotionNotificationIdBase + customerId.hashCode.abs() % 1000;
    final isAnniversary = occasion.toLowerCase() == 'anniversary';
    
    await NotificationService.scheduleNotification(
      id: notificationId,
      title: isAnniversary ? 'Happy Anniversary! 🎊' : 'Happy Birthday! 🎂',
      body: isAnniversary 
          ? 'Celebrate your special day with us! Enjoy 20% off any service.'
          : 'It\'s your special day! Treat yourself to 20% off any service.',
      scheduledDate: occasionDate,
      payload: _createPayload(
        type: AppConstants.promotionNotification,
        id: 'special_$occasion',
        action: 'view_offers',
        extra: customerId,
      ),
    );

    if (kDebugMode) {
      print('Scheduled $occasion notification for customer: $customerId');
    }
  }

  /// Cancel booking-related notifications
  static Future<void> cancelBookingNotifications(String bookingId) async {
    final bookingNotificationId = _bookingNotificationIdBase + bookingId.hashCode.abs() % 1000;
    final reminderBaseId = _reminderNotificationIdBase + bookingId.hashCode.abs() % 1000;
    
    await NotificationService.cancelNotification(bookingNotificationId);
    await NotificationService.cancelNotification(reminderBaseId);
    await NotificationService.cancelNotification(reminderBaseId + 1);

    if (kDebugMode) {
      print('Cancelled notifications for booking: $bookingId');
    }
  }

  /// Cancel promotional notification
  static Future<void> cancelPromotionalNotification(String offerId) async {
    final notificationId = _promotionNotificationIdBase + offerId.hashCode.abs() % 1000;
    await NotificationService.cancelNotification(notificationId);

    if (kDebugMode) {
      print('Cancelled promotional notification for offer: $offerId');
    }
  }

  /// Subscribe to promotional notifications
  static Future<void> subscribeToPromotions() async {
    await NotificationService.subscribeToTopic('promotions');
    await StorageService.setBool('subscribed_to_promotions', true);
    
    if (kDebugMode) {
      print('Subscribed to promotional notifications');
    }
  }

  /// Unsubscribe from promotional notifications
  static Future<void> unsubscribeFromPromotions() async {
    await NotificationService.unsubscribeFromTopic('promotions');
    await StorageService.setBool('subscribed_to_promotions', false);
    
    if (kDebugMode) {
      print('Unsubscribed from promotional notifications');
    }
  }

  /// Check if user is subscribed to promotions
  static Future<bool> isSubscribedToPromotions() async {
    return await StorageService.getBool('subscribed_to_promotions') ?? false;
  }

  /// Helper method to check if notifications should be sent
  static Future<bool> _shouldSendNotification() async {
    if (!AppConstants.enablePushNotifications) return false;
    return await NotificationService.areNotificationsEnabled();
  }

  /// Helper method to create notification payload
  static String _createPayload({
    required String type,
    required String id,
    required String action,
    String? extra,
  }) {
    return '$type|$id|$action${extra != null ? '|$extra' : ''}';
  }

  /// Helper method to format date
  static String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  /// Helper method to format time
  static String _formatTime(DateTime date) {
    final hour = date.hour > 12 ? date.hour - 12 : (date.hour == 0 ? 12 : date.hour);
    final minute = date.minute.toString().padLeft(2, '0');
    final period = date.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  /// Test notification (for development)
  static Future<void> sendTestNotification() async {
    if (kDebugMode) {
      await NotificationService.scheduleNotification(
        id: 9999,
        title: 'Test Notification 🧪',
        body: 'This is a test notification from Flix Salon & SPA',
        scheduledDate: DateTime.now().add(const Duration(seconds: 2)),
        payload: _createPayload(
          type: AppConstants.generalNotification,
          id: 'test',
          action: 'none',
        ),
      );
      print('Sent test notification');
    }
  }
}
