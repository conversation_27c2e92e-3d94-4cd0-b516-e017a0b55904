import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/routes/app_routes.dart';
import 'booking_history_screen.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/recent_bookings_card.dart';
import '../widgets/loyalty_points_card.dart';
import '../widgets/quick_actions_grid.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _dashboardData = {};

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));
    
    setState(() {
      _dashboardData = {
        'user': {
          'name': '<PERSON>',
          'email': '<EMAIL>',
          'phone': '+****************',
          'memberSince': '2023-01-15',
          'profileImage': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200',
        },
        'stats': {
          'totalBookings': 24,
          'completedServices': 22,
          'cancelledBookings': 2,
          'totalSpent': 2850.0,
        },
        'loyaltyPoints': {
          'current': 1250,
          'lifetime': 3400,
          'nextRewardAt': 1500,
          'tier': 'Gold',
        },
        'recentBookings': [
          {
            'id': 'booking_001',
            'serviceName': 'Premium Facial Treatment',
            'date': '2024-01-15',
            'time': '2:00 PM',
            'status': 'completed',
            'amount': 150.0,
            'staffName': 'Emily Davis',
          },
          {
            'id': 'booking_002',
            'serviceName': 'Hair Cut & Styling',
            'date': '2024-01-08',
            'time': '10:30 AM',
            'status': 'completed',
            'amount': 80.0,
            'staffName': 'Sarah Wilson',
          },
          {
            'id': 'booking_003',
            'serviceName': 'Manicure & Pedicure',
            'date': '2024-01-22',
            'time': '3:30 PM',
            'status': 'upcoming',
            'amount': 120.0,
            'staffName': 'Jessica Brown',
          },
        ],
        'upcomingBookings': [
          {
            'id': 'booking_003',
            'serviceName': 'Manicure & Pedicure',
            'date': '2024-01-22',
            'time': '3:30 PM',
            'staffName': 'Jessica Brown',
            'amount': 120.0,
          },
        ],
      };
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Dashboard',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.notifications_outlined,
              color: ThemeConfig.textPrimary,
            ),
            onPressed: () {
              // Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(
              Icons.person_outline,
              color: ThemeConfig.textPrimary,
            ),
            onPressed: () {
              AppRoutes.pushNamed(context, AppRoutes.profile);
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: ThemeConfig.primaryColor,
              ),
            )
          : RefreshIndicator(
              color: ThemeConfig.primaryColor,
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Section
                    _buildWelcomeSection(),
                    
                    const SizedBox(height: 24),
                    
                    // Quick Actions
                    QuickActionsGrid(
                      onBookNow: () {
                        AppRoutes.pushNamed(context, AppRoutes.services);
                      },
                      onViewBookings: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const BookingHistoryScreen(),
                          ),
                        );
                      },
                      onViewOffers: () {
                        AppRoutes.pushNamed(context, AppRoutes.offers);
                      },
                      onContactUs: () {
                        AppRoutes.pushNamed(context, AppRoutes.contact);
                      },
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Loyalty Points
                    LoyaltyPointsCard(
                      loyaltyData: _dashboardData['loyaltyPoints'],
                      onViewRewards: () {
                        // TODO: Navigate to rewards screen when implemented
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Rewards screen coming soon!'),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Dashboard Stats
                    DashboardStatsCard(
                      stats: _dashboardData['stats'],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Recent Bookings
                    RecentBookingsCard(
                      bookings: _dashboardData['recentBookings'],
                      onViewAll: () {
                        AppRoutes.pushNamed(context, AppRoutes.bookingHistory);
                      },
                      onBookingTap: (booking) {
                        AppRoutes.pushNamed(
                          context,
                          AppRoutes.bookingDetails,
                          arguments: {'bookingId': booking['id']},
                        );
                      },
                    ),
                    
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWelcomeSection() {
    final user = _dashboardData['user'] ?? {};
    final upcomingBookings = _dashboardData['upcomingBookings'] ?? [];
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ThemeConfig.primaryColor,
            ThemeConfig.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ThemeConfig.primaryColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundImage: NetworkImage(user['profileImage'] ?? ''),
                onBackgroundImageError: (error, stackTrace) {},
                child: user['profileImage']?.isEmpty ?? true
                    ? Text(
                        user['name']?[0] ?? 'U',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back,',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user['name'] ?? 'User',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          if (upcomingBookings.isNotEmpty) ...[
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        color: Colors.white.withValues(alpha: 0.9),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Next Appointment',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    upcomingBookings.first['serviceName'] ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${upcomingBookings.first['date']} at ${upcomingBookings.first['time']}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
