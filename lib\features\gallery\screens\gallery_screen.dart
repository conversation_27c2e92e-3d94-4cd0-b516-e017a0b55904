import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';

class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _galleryImages = [];
  List<Map<String, dynamic>> _reviews = [];
  List<String> _categories = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadGalleryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadGalleryData() async {
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _categories = [
        'All',
        'Hair Styling',
        'Facial Treatments',
        'Nail Care',
        'Massage',
        'Spa Treatments',
        'Salon Interior',
      ];

      _galleryImages = [
        {
          'id': 'img_001',
          'url': 'https://images.unsplash.com/photo-**********-8baeececf3df?w=400',
          'title': 'Professional Hair Styling',
          'category': 'Hair Styling',
          'description': 'Expert hair cutting and styling services',
        },
        {
          'id': 'img_002',
          'url': 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400',
          'title': 'Relaxing Facial Treatment',
          'category': 'Facial Treatments',
          'description': 'Rejuvenating facial treatments for all skin types',
        },
        {
          'id': 'img_003',
          'url': 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400',
          'title': 'Professional Manicure',
          'category': 'Nail Care',
          'description': 'Perfect nail care and beautiful designs',
        },
        {
          'id': 'img_004',
          'url': 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400',
          'title': 'Therapeutic Massage',
          'category': 'Massage',
          'description': 'Relaxing and therapeutic massage treatments',
        },
        {
          'id': 'img_005',
          'url': 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400',
          'title': 'Luxury Spa Experience',
          'category': 'Spa Treatments',
          'description': 'Complete spa packages for ultimate relaxation',
        },
        {
          'id': 'img_006',
          'url': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400',
          'title': 'Modern Salon Interior',
          'category': 'Salon Interior',
          'description': 'Our beautiful and modern salon space',
        },
        {
          'id': 'img_007',
          'url': 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400',
          'title': 'Hair Color Transformation',
          'category': 'Hair Styling',
          'description': 'Professional hair coloring and highlights',
        },
        {
          'id': 'img_008',
          'url': 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400',
          'title': 'Eyebrow Shaping',
          'category': 'Facial Treatments',
          'description': 'Perfect eyebrow threading and shaping',
        },
      ];

      _reviews = [
        {
          'id': 'review_001',
          'customerName': 'Sarah Johnson',
          'customerImage': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
          'rating': 5.0,
          'date': '2024-01-15',
          'service': 'Premium Facial Treatment',
          'review': 'Absolutely amazing experience! The staff was professional and the facial left my skin glowing. Highly recommend!',
        },
        {
          'id': 'review_002',
          'customerName': 'Emily Davis',
          'customerImage': 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100',
          'rating': 4.8,
          'date': '2024-01-12',
          'service': 'Hair Cut & Styling',
          'review': 'Love my new haircut! The stylist really understood what I wanted and delivered perfectly. Great service!',
        },
        {
          'id': 'review_003',
          'customerName': 'Jessica Brown',
          'customerImage': 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=100',
          'rating': 5.0,
          'date': '2024-01-10',
          'service': 'Manicure & Pedicure',
          'review': 'The best nail salon in town! Clean, professional, and the results are always perfect. Been coming here for months!',
        },
        {
          'id': 'review_004',
          'customerName': 'Lisa Chen',
          'customerImage': 'https://images.unsplash.com/photo-1607990281513-2c110a25bd8c?w=100',
          'rating': 4.9,
          'date': '2024-01-08',
          'service': 'Deep Tissue Massage',
          'review': 'Incredible massage therapy! Really helped with my back pain. The therapist was skilled and professional.',
        },
        {
          'id': 'review_005',
          'customerName': 'Amanda Wilson',
          'customerImage': 'https://images.unsplash.com/photo-1594824388853-d0c2d5e5b6b8?w=100',
          'rating': 4.7,
          'date': '2024-01-05',
          'service': 'Eyebrow Threading',
          'review': 'Quick and painless eyebrow threading. Great attention to detail and very reasonable prices.',
        },
      ];

      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Gallery & Reviews',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: ThemeConfig.primaryColor,
          unselectedLabelColor: ThemeConfig.textSecondary,
          indicatorColor: ThemeConfig.primaryColor,
          tabs: const [
            Tab(
              icon: Icon(Icons.photo_library),
              text: 'Gallery',
            ),
            Tab(
              icon: Icon(Icons.star),
              text: 'Reviews',
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: ThemeConfig.primaryColor,
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildGalleryTab(),
                _buildReviewsTab(),
              ],
            ),
    );
  }

  Widget _buildGalleryTab() {
    return Column(
      children: [
        // Category Filter
        _buildCategoryFilter(),

        // Gallery Grid
        Expanded(
          child: RefreshIndicator(
            color: ThemeConfig.primaryColor,
            onRefresh: _loadGalleryData,
            child: _buildGalleryGrid(),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = index == 0; // For demo, first category is selected

          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                // TODO: Implement category filtering
              },
              backgroundColor: Colors.white,
              selectedColor: ThemeConfig.primaryColor.withValues(alpha: 0.1),
              labelStyle: TextStyle(
                color: isSelected ? ThemeConfig.primaryColor : ThemeConfig.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              side: BorderSide(
                color: isSelected ? ThemeConfig.primaryColor : Colors.grey[300]!,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildGalleryGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: _galleryImages.length,
      itemBuilder: (context, index) {
        final image = _galleryImages[index];
        return _buildGalleryCard(image);
      },
    );
  }

  Widget _buildGalleryCard(Map<String, dynamic> image) {
    return GestureDetector(
      onTap: () {
        _showImageDialog(image);
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
                child: Image.network(
                  image['url'] ?? '',
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image,
                        color: Colors.grey,
                        size: 48,
                      ),
                    );
                  },
                ),
              ),
            ),

            // Content
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      image['title'] ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: ThemeConfig.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: ThemeConfig.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        image['category'] ?? '',
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: ThemeConfig.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReviewsTab() {
    return RefreshIndicator(
      color: ThemeConfig.primaryColor,
      onRefresh: _loadGalleryData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _reviews.length,
        itemBuilder: (context, index) {
          final review = _reviews[index];
          return _buildReviewCard(review);
        },
      ),
    );
  }

  Widget _buildReviewCard(Map<String, dynamic> review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage: NetworkImage(review['customerImage'] ?? ''),
                onBackgroundImageError: (error, stackTrace) {},
                child: review['customerImage']?.isEmpty ?? true
                    ? Text(
                        review['customerName']?[0] ?? 'U',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review['customerName'] ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: ThemeConfig.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        _buildStarRating(review['rating']?.toDouble() ?? 0.0),
                        const SizedBox(width: 8),
                        Text(
                          review['date'] ?? '',
                          style: const TextStyle(
                            fontSize: 12,
                            color: ThemeConfig.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Service
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: ThemeConfig.secondaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              review['service'] ?? '',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: ThemeConfig.secondaryColor,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Review Text
          Text(
            review['review'] ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: ThemeConfig.textPrimary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor()
              ? Icons.star
              : index < rating
                  ? Icons.star_half
                  : Icons.star_border,
          color: Colors.amber,
          size: 16,
        );
      }),
    );
  }

  void _showImageDialog(Map<String, dynamic> image) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Close button
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),

            // Image
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.network(
                  image['url'] ?? '',
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 200,
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image,
                        color: Colors.grey,
                        size: 64,
                      ),
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Image info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    image['title'] ?? '',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: ThemeConfig.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    image['description'] ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      color: ThemeConfig.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
