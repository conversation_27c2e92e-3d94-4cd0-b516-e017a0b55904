{"inputs": ["E:\\flutter\\flutter_windows_3.32.0-stable\\flutter\\bin\\cache\\engine.stamp"], "outputs": ["E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\flutter.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\canvaskit.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\canvaskit.js.symbols", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\canvaskit.wasm", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\chromium\\canvaskit.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\chromium\\canvaskit.js.symbols", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\chromium\\canvaskit.wasm", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\skwasm.js", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\skwasm.js.symbols", "E:\\FlixApp\\.dart_tool\\flutter_build\\9534c34e742c910876072db3f339c567\\canvaskit\\skwasm.wasm"]}