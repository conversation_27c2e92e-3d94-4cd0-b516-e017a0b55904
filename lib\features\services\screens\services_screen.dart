import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/routes/app_routes.dart';
import '../widgets/service_card.dart';
import '../widgets/service_category_filter.dart';

class ServicesScreen extends StatefulWidget {
  final String? category;
  final String? subcategory;

  const ServicesScreen({
    super.key,
    this.category,
    this.subcategory,
  });

  @override
  State<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends State<ServicesScreen> {
  String _selectedCategory = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  final List<Map<String, dynamic>> _categories = [
    {'id': 'all', 'name': 'All Services', 'icon': Icons.spa},
    {'id': 'hair', 'name': 'Hair Care', 'icon': Icons.content_cut},
    {'id': 'skin', 'name': 'Skin Care', 'icon': Icons.face},
    {'id': 'nail', 'name': 'Nail Care', 'icon': Icons.back_hand},
    {'id': 'massage', 'name': 'Massage', 'icon': Icons.spa},
    {'id': 'facial', 'name': 'Facial', 'icon': Icons.face_retouching_natural},
  ];

  final List<Map<String, dynamic>> _services = [
    {
      'id': 'service_1',
      'name': 'Premium Facial Treatment',
      'category': 'facial',
      'price': 150.0,
      'duration': 60,
      'description': 'Deep cleansing facial with premium products',
      'image': 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?w=400',
      'rating': 4.8,
      'reviews': 124,
    },
    {
      'id': 'service_2',
      'name': 'Hair Cut & Styling',
      'category': 'hair',
      'price': 80.0,
      'duration': 45,
      'description': 'Professional hair cut and styling service',
      'image': 'https://images.unsplash.com/photo-1562322140-8baeececf3df?w=400',
      'rating': 4.9,
      'reviews': 89,
    },
    {
      'id': 'service_3',
      'name': 'Manicure & Pedicure',
      'category': 'nail',
      'price': 120.0,
      'duration': 90,
      'description': 'Complete nail care with gel polish',
      'image': 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400',
      'rating': 4.7,
      'reviews': 156,
    },
    {
      'id': 'service_4',
      'name': 'Relaxing Body Massage',
      'category': 'massage',
      'price': 200.0,
      'duration': 75,
      'description': 'Full body relaxation massage therapy',
      'image': 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400',
      'rating': 4.9,
      'reviews': 203,
    },
    {
      'id': 'service_5',
      'name': 'Anti-Aging Facial',
      'category': 'skin',
      'price': 180.0,
      'duration': 75,
      'description': 'Advanced anti-aging treatment',
      'image': 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400',
      'rating': 4.8,
      'reviews': 98,
    },
    {
      'id': 'service_6',
      'name': 'Hair Color & Highlights',
      'category': 'hair',
      'price': 250.0,
      'duration': 120,
      'description': 'Professional hair coloring service',
      'image': 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400',
      'rating': 4.6,
      'reviews': 67,
    },
  ];

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _selectedCategory = widget.category!;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> get _filteredServices {
    return _services.where((service) {
      final matchesCategory = _selectedCategory == 'all' ||
                             service['category'] == _selectedCategory;
      final matchesSearch = _searchQuery.isEmpty ||
                           service['name'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
                           service['description'].toLowerCase().contains(_searchQuery.toLowerCase());
      return matchesCategory && matchesSearch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Services',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: ThemeConfig.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: ThemeConfig.textPrimary),
            onPressed: () {
              // Toggle search
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: 'Search services...',
                prefixIcon: const Icon(Icons.search, color: ThemeConfig.textSecondary),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: ThemeConfig.primaryColor),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ),

          // Category Filter
          ServiceCategoryFilter(
            categories: _categories,
            selectedCategory: _selectedCategory,
            onCategorySelected: (category) {
              setState(() {
                _selectedCategory = category;
              });
            },
          ),

          // Services List
          Expanded(
            child: _filteredServices.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: ThemeConfig.textSecondary,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No services found',
                          style: TextStyle(
                            fontSize: 18,
                            color: ThemeConfig.textSecondary,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Try adjusting your search or filters',
                          style: TextStyle(
                            fontSize: 14,
                            color: ThemeConfig.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredServices.length,
                    itemBuilder: (context, index) {
                      final service = _filteredServices[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: ServiceCard(
                          service: service,
                          onTap: () {
                            AppRoutes.pushNamed(
                              context,
                              AppRoutes.serviceDetails,
                              arguments: {'serviceId': service['id']},
                            );
                          },
                          onBookTap: () {
                            AppRoutes.pushNamed(
                              context,
                              AppRoutes.booking,
                              arguments: {'serviceId': service['id']},
                            );
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
