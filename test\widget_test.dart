import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FlixApp Basic Tests', () {
    testWidgets('Basic widget should build without errors', (WidgetTester tester) async {
      // Create a simple test app
      final testApp = MaterialApp(
        title: 'Flix App Test',
        home: const Scaffold(
          body: Center(
            child: Text('Flix App Test'),
          ),
        ),
      );

      // Build the app and trigger a frame
      await tester.pumpWidget(testApp);

      // Verify that the app builds successfully
      expect(find.text('Flix App Test'), findsOneWidget);
    });

    testWidgets('Material app should have correct title', (WidgetTester tester) async {
      final testApp = MaterialApp(
        title: 'Flix Customer App',
        home: Scaffold(
          appBar: AppBar(title: const Text('Flix')),
          body: const Center(child: Text('Welcome to Flix')),
        ),
      );

      await tester.pumpWidget(testApp);

      expect(find.text('Flix'), findsOneWidget);
      expect(find.text('Welcome to Flix'), findsOneWidget);
    });
  });
}
