import 'package:flutter/material.dart';

import '../../../core/config/theme_config.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/services/notification_manager.dart';

class BookingConfirmationScreen extends StatefulWidget {
  final Map<String, dynamic> bookingData;

  const BookingConfirmationScreen({
    super.key,
    required this.bookingData,
  });

  @override
  State<BookingConfirmationScreen> createState() => _BookingConfirmationScreenState();
}

class _BookingConfirmationScreenState extends State<BookingConfirmationScreen> {
  @override
  void initState() {
    super.initState();
    _scheduleNotifications();
  }

  void _scheduleNotifications() async {
    final bookingId = widget.bookingData['bookingId'] ?? '';
    final service = widget.bookingData['service'] ?? {};
    final date = widget.bookingData['date'] as DateTime?;
    final customerName = 'Customer'; // This should come from user data

    if (bookingId.isNotEmpty && service.isNotEmpty && date != null) {
      final serviceName = service['name'] ?? 'Service';

      // Schedule booking confirmation notification
      await NotificationManager.scheduleBookingConfirmation(
        bookingId: bookingId,
        serviceName: serviceName,
        appointmentDate: date,
        customerName: customerName,
      );

      // Schedule booking reminder notifications
      await NotificationManager.scheduleBookingReminders(
        bookingId: bookingId,
        serviceName: serviceName,
        appointmentDate: date,
        customerName: customerName,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final bookingId = widget.bookingData['bookingId'] ?? '';
    final service = widget.bookingData['service'] ?? {};
    final staff = widget.bookingData['staff'] ?? {};
    final date = widget.bookingData['date'] as DateTime?;
    final time = widget.bookingData['time'] ?? '';

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: const Text(
          'Booking Confirmed',
          style: TextStyle(
            color: ThemeConfig.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Success Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                size: 80,
                color: Colors.green,
              ),
            ),

            const SizedBox(height: 24),

            const Text(
              'Booking Confirmed!',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: ThemeConfig.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            const Text(
              'Your appointment has been successfully booked.',
              style: TextStyle(
                fontSize: 16,
                color: ThemeConfig.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Booking Details Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Booking ID
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Booking ID',
                        style: TextStyle(
                          fontSize: 14,
                          color: ThemeConfig.textSecondary,
                        ),
                      ),
                      Text(
                        bookingId.length > 8
                            ? bookingId.substring(bookingId.length - 8)
                            : bookingId,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: ThemeConfig.primaryColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Service
                  _buildDetailRow(
                    'Service',
                    service['name'] ?? '',
                    '${service['duration'] ?? 0} minutes',
                  ),

                  const SizedBox(height: 16),

                  // Staff
                  _buildDetailRow(
                    'Specialist',
                    staff['name'] ?? '',
                    staff['title'] ?? '',
                  ),

                  const SizedBox(height: 16),

                  // Date
                  _buildDetailRow(
                    'Date',
                    date != null
                        ? '${date.day}/${date.month}/${date.year}'
                        : '',
                    _getDayOfWeek(date),
                  ),

                  const SizedBox(height: 16),

                  // Time
                  _buildDetailRow(
                    'Time',
                    time,
                    '',
                  ),

                  const SizedBox(height: 20),

                  // Divider
                  Container(
                    height: 1,
                    color: Colors.grey[300],
                  ),

                  const SizedBox(height: 20),

                  // Total
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total Amount',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: ThemeConfig.textPrimary,
                        ),
                      ),
                      Text(
                        '\$${service['price']?.toStringAsFixed(0) ?? '0'}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: ThemeConfig.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Important Notes
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue[700],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Important Notes',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '• Please arrive 10 minutes before your appointment\n'
                    '• Cancellation must be made 24 hours in advance\n'
                    '• You will receive a reminder notification\n'
                    '• Bring a valid ID for verification',
                    style: TextStyle(
                      fontSize: 14,
                      color: ThemeConfig.textSecondary,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Action Buttons
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushNamedAndRemoveUntil(
                        AppRoutes.main,
                        (route) => false,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ThemeConfig.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Back to Home',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () {
                      AppRoutes.pushNamed(
                        context,
                        AppRoutes.bookingHistory,
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: const BorderSide(color: ThemeConfig.primaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'View My Bookings',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: ThemeConfig.primaryColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, String subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: ThemeConfig.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ThemeConfig.textPrimary,
          ),
        ),
        if (subtitle.isNotEmpty) ...[
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              color: ThemeConfig.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  String _getDayOfWeek(DateTime? date) {
    if (date == null) return '';

    const days = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday',
      'Friday', 'Saturday', 'Sunday'
    ];

    return days[date.weekday - 1];
  }
}
